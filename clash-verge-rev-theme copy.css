/*



*/
section {
  background-color: rgba(255, 255, 255, 0) !important;
}


html {
font-size: 22px;
-webkit-font-smoothing: antialiased;
-moz-osx-font-smoothing: grayscale;
--background-color: #f0f0f000 !important;
}

.base-container {
  background-color: #f0f0f000 !important;
}


.MuiTypography-root {
  background-color: rgba(0, 0, 0, 0) !important;
}


/* MuiBox 基础样式 - 全局透明背景 */
.MuiBox-root {
  background-color: rgba(255, 255, 255, 0) !important;
}


/* 全局背景图 - 透明度0.8 */
.css-1li7dvq {
  position: relative;
  background-color: rgba(255, 255, 255, 0) !important;
}

.css-1li7dvq::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('https://img.paulzzh.com/touhou/random?site=all&size=pc') !important;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 1;
  z-index: -2;
}

/*浅色背景底色*/
.css-1li7dvq::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: -1;
}

.css-l3ykv8 {
  position: relative;
  background-color: rgba(255, 255, 255, 0) !important;
  color: rgb(239, 239, 239) !important;
}

.css-l3ykv8::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('https://img.paulzzh.com/touhou/random?site=all&size=pc') !important;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 1;
  z-index: -2;
}

/*深色背景底色*/
.css-l3ykv8::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: -1;
}


/* 主题模式和变量定义 */
:root {
/* 浅色模式变量 */

--border-light: rgba(0, 0, 0, 0.08);
--shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
--shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
--shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.2);


/* 布局变量 */
--base-spacing: 1.5rem;
--grid-gap: 1.2rem;
--card-min-width: 280px;
--sidebar-width: 280px;
--header-height: 64px;
--border-radius: 12px;
--border-radius-small: 8px;
--border-radius-large: 16px;
--nav-item-width: 220px;
--nav-item-border-radius: 30px;
--card-bg-light: rgba(255, 255, 255, 0.85);
--card-bg-dark: rgba(33, 33, 33, 0.85);
--card-bg-light-hover: rgba(255, 255, 255, 0.05);
--card-bg-dark-hover: rgba(33, 33, 33, 0.05);

/* 粒子效果变量 */
--particle-size: 2px;
--particle-colors:
  #FF0000, #FF7F00, #FFFF00, #00FF00, #0000FF, #4B0082, #9400D3, /* 红橙黄绿蓝靛紫 */
  #FF1493, #00BFFF, #32CD32, #FFD700, #ADFF2F, #FF4500, #8A2BE2, /* 亮粉，深天蓝，绿黄，金，青黄，橙红，蓝紫 */
  #F08080, #90EE90, #ADD8E6, #FFFFE0, #FFC0CB, #DA70D6, #B0E0E6, /* 浅珊瑚，浅绿，浅蓝，浅黄，粉，紫罗兰，粉蓝 */
  #FF69B4, #EE82EE, #87CEEB, #7CFC00, #FFDAB9, #BA55D3, #40E0D0, /* 热粉，紫罗兰，天蓝，草绿，桃，中兰，青色 */
  #FF8C00, #00CED1, #9932CC, #FF6347, #4682B4, #D2691E, #6A5ACD; /* 暗橙，暗青，暗紫，番茄红，钢蓝，巧克力，板岩蓝 */
}

/* 深色模式变量 */
@media (prefers-color-scheme: dark) {
:root:not([data-theme="light"]):not(.light-mode) {

  --text-secondary: rgba(255, 255, 255, 0.9);
  --text-tertiary: rgba(255, 255, 255, 0.7);
  --text-inverse: rgba(33, 37, 41, 0.95);

  --border-light: rgba(255, 255, 255, 0.08);

  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.3);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.4);
  --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.5);


}


/* 左侧导航栏 */
.layout__left{
flex: 0 0 var(--sidebar-width);
background: var(--background-glass);
backdrop-filter: var(--blur-medium);
border-right: 1px solid var(--border-light);
padding: var(--base-spacing);
min-width: 360px;
max-width: 420px;
box-shadow: var(--shadow-light);
position: relative;
z-index: 10;
}


/* ========================================
 响应式网格系统
 ======================================== */

/* 响应式网格容器 */
.responsive-grid, .grid-container {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(var(--card-min-width), 1fr));
gap: var(--grid-gap);
padding: var(--grid-gap);
width: 100%;
align-items: start;
}

/* 网格项目 */
.grid-item {
display: flex;
flex-direction: column;
min-height: 0;
}

/* 菜单系统网格 */
.the-menu, .menu-grid {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
gap: var(--base-spacing);
margin: var(--base-spacing) 0;
}


/* ========================================
 Material-UI 组件优化
 ======================================== */

/* 按钮组件 */
.MuiButton-root, .MuiButtonBase-root {
border: 1px solid var(--border-light) !important;
backdrop-filter: var(--blur-light);
border-radius: var(--border-radius-small) !important;

box-shadow: var(--shadow-light) !important;
}

/* ========================================
 赛博朋克卡片悬停特效
 ======================================== */

/* 定义赛博朋克风格的颜色和动画变量 */
:root {
--cyber-c1: #00fffc; /* 亮青色 */
--cyber-c2: #ff00ff; /* 品红色 */
--cyber-c3: #faff00; /* 亮黄色 */
--cyber-border-angle: 0deg;
}

/* 注册 CSS 变量以实现平滑动画 */
@property --cyber-border-angle {
syntax: '<angle>';
inherits: false;
initial-value: 0deg;
}

/* 统一所有卡片和按钮的基础样式，为特效做准备 */
.MuiBox-root .css-1ow8u3y, .css-1rgmi2n, .css-bjjbb7, .css-hds0vx,
.css-aafiep, .css-xd8r7u, .css-ya2z3b, .css-8sla8j, .css-ulr2qx, .css-17rlh6j,
.main .bg-primary-foreground, .side .bg-primary-foreground,
.main .bg-foreground, .side .bg-foreground,
.main .bg-content1, .side .bg-content1,
.main .bg-default, .side .bg-default,
.MuiButton-root, .MuiListItemButton-root,
.MuiBox-root.css-td54yc, .MuiBox-root.css-qhetv7 {
transition: transform 0.3s ease, background-color 0.3s ease !important;
position: relative !important;
overflow: hidden !important; /* 包含内部特效 */
z-index: 1 !important;
}

/* 为特定卡片设置背景和圆角 */
.MuiBox-root .css-1ow8u3y, .css-1rgmi2n, .css-bjjbb7, .css-hds0vx,
.css-aafiep, .css-xd8r7u, .css-ya2z3b, .css-8sla8j, .css-ulr2qx, .css-17rlh6j,
.MuiBox-root.css-td54yc, .MuiBox-root.css-qhetv7 {
background-color: var(--card-bg-light) !important;
border-radius: var(--border-radius) !important;
}

/* 深色模式下的卡片背景 */
@media (prefers-color-scheme: dark) {
.MuiBox-root .css-1ow8u3y, .css-1rgmi2n, .css-bjjbb7, .css-hds0vx,
.css-aafiep, .css-xd8r7u, .css-ya2z3b, .css-8sla8j, .css-ulr2qx, .css-17rlh6j,
.MuiBox-root.css-td54yc, .MuiBox-root.css-qhetv7 {
  background-color: var(--card-bg-dark) !important;
}
}

/* 动态边框的容器 - 使用 ::before */
.MuiBox-root .css-1ow8u3y::before, .css-1rgmi2n::before, .css-bjjbb7::before, .css-hds0vx::before,
.css-aafiep::before, .css-xd8r7u::before, .css-ya2z3b::before, .css-8sla8j::before, .css-ulr2qx::before, .css-17rlh6j::before,
.main .bg-primary-foreground::before, .side .bg-primary-foreground::before,
.main .bg-foreground::before, .side .bg-foreground::before,
.main .bg-content1::before, .side .bg-content1::before,
.main .bg-default::before, .side .bg-default::before,
.MuiButton-root::before, .MuiListItemButton-root::before,
.MuiBox-root.css-td54yc::before, .MuiBox-root.css-qhetv7::before {
content: '' !important;
position: absolute !important;
top: 0; left: 0; right: 0; bottom: 0;
border-radius: inherit !important; /* 继承父元素的圆角 */
padding: 2px !important; /* 边框宽度 */
background: conic-gradient(from var(--cyber-border-angle), var(--cyber-c2), var(--cyber-c1), var(--cyber-c3), var(--cyber-c2)) !important;
-webkit-mask:
  linear-gradient(#fff 0 0) content-box,
  linear-gradient(#fff 0 0) !important;
mask:
  linear-gradient(#fff 0 0) content-box,
  linear-gradient(#fff 0 0) !important;
-webkit-mask-composite: xor !important;
mask-composite: exclude !important;
animation: cyberpunk-border-flow 4s linear infinite !important;
opacity: 0 !important;
transition: opacity 0.4s ease-in-out !important;
z-index: -1 !important;
pointer-events: none !important;
}


/* 粒子效果样式 */
.MuiBox-root.css-td54yc::after, .MuiBox-root.css-qhetv7::after {
content: '' !important;
position: absolute !important;
top: 0;
left: 0;
right: 0;
bottom: 0;
pointer-events: none !important;
z-index: 0 !important;
border-radius: inherit !important;
box-shadow:
  /* 50+ particles with different positions and colors */
  /* Row 1 */
  10% 10% var(--particle-size) 0px var(--particle-colors, #FF0000),
  15% 50% var(--particle-size) 0px var(--particle-colors, #00FF00),
  20% 90% var(--particle-size) 0px var(--particle-colors, #0000FF),
  25% 20% var(--particle-size) 0px var(--particle-colors, #FFFF00),
  30% 60% var(--particle-size) 0px var(--particle-colors, #FF7F00),
  35% 05% var(--particle-size) 0px var(--particle-colors, #4B0082),
  40% 45% var(--particle-size) 0px var(--particle-colors, #9400D3),
  45% 85% var(--particle-size) 0px var(--particle-colors, #FF1493),
  50% 15% var(--particle-size) 0px var(--particle-colors, #00BFFF),
  55% 55% var(--particle-size) 0px var(--particle-colors, #32CD32),
  60% 95% var(--particle-size) 0px var(--particle-colors, #FFD700),
  65% 25% var(--particle-size) 0px var(--particle-colors, #ADFF2F),
  70% 65% var(--particle-size) 0px var(--particle-colors, #FF4500),
  75% 00% var(--particle-size) 0px var(--particle-colors, #8A2BE2),
  80% 40% var(--particle-size) 0px var(--particle-colors, #F08080),
  85% 80% var(--particle-size) 0px var(--particle-colors, #90EE90),
  90% 05% var(--particle-size) 0px var(--particle-colors, #ADD8E6),
  95% 35% var(--particle-size) 0px var(--particle-colors, #FFFFE0),
  05% 75% var(--particle-size) 0px var(--particle-colors, #FFC0CB),
  12% 12% var(--particle-size) 0px var(--particle-colors, #DA70D6),
  18% 52% var(--particle-size) 0px var(--particle-colors, #B0E0E6),
  22% 92% var(--particle-size) 0px var(--particle-colors, #FF69B4),
  28% 22% var(--particle-size) 0px var(--particle-colors, #EE82EE),
  32% 62% var(--particle-size) 0px var(--particle-colors, #87CEEB),
  38% 07% var(--particle-size) 0px var(--particle-colors, #7CFC00),
  42% 47% var(--particle-size) 0px var(--particle-colors, #FFDAB9),
  48% 87% var(--particle-size) 0px var(--particle-colors, #BA55D3),
  52% 17% var(--particle-size) 0px var(--particle-colors, #40E0D0),
  58% 57% var(--particle-size) 0px var(--particle-colors, #FF8C00),
  62% 97% var(--particle-size) 0px var(--particle-colors, #00CED1),
  68% 27% var(--particle-size) 0px var(--particle-colors, #9932CC),
  72% 67% var(--particle-size) 0px var(--particle-colors, #FF6347),
  78% 02% var(--particle-size) 0px var(--particle-colors, #4682B4),
  82% 42% var(--particle-size) 0px var(--particle-colors, #D2691E),
  88% 82% var(--particle-size) 0px var(--particle-colors, #6A5ACD),
  08% 18% var(--particle-size) 0px var(--particle-colors, #FF0000),
  13% 58% var(--particle-size) 0px var(--particle-colors, #00FF00),
  17% 98% var(--particle-size) 0px var(--particle-colors, #0000FF),
  23% 28% var(--particle-size) 0px var(--particle-colors, #FFFF00),
  27% 68% var(--particle-size) 0px var(--particle-colors, #FF7F00),
  33% 03% var(--particle-size) 0px var(--particle-colors, #4B0082),
  37% 43% var(--particle-size) 0px var(--particle-colors, #9400D3),
  43% 83% var(--particle-size) 0px var(--particle-colors, #FF1493),
  47% 13% var(--particle-size) 0px var(--particle-colors, #00BFFF),
  53% 53% var(--particle-size) 0px var(--particle-colors, #32CD32),
  57% 93% var(--particle-size) 0px var(--particle-colors, #FFD700),
  63% 23% var(--particle-size) 0px var(--particle-colors, #ADFF2F),
  67% 63% var(--particle-size) 0px var(--particle-colors, #FF4500),
  73% 08% var(--particle-size) 0px var(--particle-colors, #8A2BE2),
  77% 48% var(--particle-size) 0px var(--particle-colors, #F08080),
  83% 88% var(--particle-size) 0px var(--particle-colors, #90EE90);
}

/* Specific card type animations */
.MuiBox-root.css-td54yc::after {
  animation: particle-move-linear 15s linear infinite -2s forwards !important;
}
.MuiBox-root.css-qhetv7::after {
  animation: particle-move-wave 18s ease-in-out infinite -5s forwards !important;
}

/* Additional random speed combinations for more variety (re-using animation types) */
.MuiBox-root.css-td54yc:nth-child(2)::after { animation-duration: 13s !important; animation-delay: -7s !important; }
.MuiBox-root.css-qhetv7:nth-child(2)::after { animation-duration: 21s !important; animation-delay: -11s !important; }
.MuiBox-root.css-td54yc:nth-child(3)::after { animation-duration: 17s !important; animation-delay: -9s !important; }
.MuiBox-root.css-qhetv7:nth-child(3)::after { animation-duration: 19s !important; animation-delay: -14s !important; }
.MuiBox-root.css-td54yc:nth-child(4)::after { animation: particle-move-zigzag 25s ease-out infinite -10s forwards !important; animation-duration: 11s !important; animation-delay: -2s !important; }
.MuiBox-root.css-qhetv7:nth-child(4)::after { animation: particle-move-fade-up 10s ease-in infinite -3s forwards !important; animation-duration: 23s !important; animation-delay: -16s !important; }
.MuiBox-root.css-td54yc:nth-child(5)::after { animation: particle-move-bounce 14s ease-out infinite -6s forwards !important; animation-duration: 16s !important; animation-delay: -8s !important; }
.MuiBox-root.css-qhetv7:nth-child(5)::after { animation: particle-move-drift 22s linear infinite -12s forwards !important; animation-duration: 26s !important; animation-delay: -13s !important; }
.MuiBox-root.css-td54yc:nth-child(6)::after { animation: particle-move-pulse 16s ease-in-out infinite -4s forwards !important; animation-duration: 12s !important; animation-delay: -5s !important; }
.MuiBox-root.css-qhetv7:nth-child(6)::after { animation: particle-move-orbit 28s linear infinite -15s forwards !important; animation-duration: 29s !important; animation-delay: -18s !important; }
.MuiBox-root.css-td54yc:nth-child(7)::after { animation-duration: 10s !important; animation-delay: -1s !important; }
.MuiBox-root.css-qhetv7:nth-child(7)::after { animation-duration: 24s !important; animation-delay: -17s !important; }
.MuiBox-root.css-td54yc:nth-child(8)::after { animation-duration: 14s !important; animation-delay: -3s !important; }
.MuiBox-root.css-qhetv7:nth-child(8)::after { animation-duration: 27s !important; animation-delay: -19s !important; }
.MuiBox-root.css-td54yc:nth-child(9)::after { animation-duration: 9s !important; animation-delay: -0.5s !important; }
.MuiBox-root.css-qhetv7:nth-child(9)::after { animation-duration: 30s !important; animation-delay: -20s !important; }
.MuiBox-root.css-td54yc:nth-child(10)::after { animation-duration: 17.5s !important; animation-delay: -9.5s !important; }
.MuiBox-root.css-qhetv7:nth-child(10)::after { animation-duration: 32s !important; animation-delay: -22s !important; }
.MuiBox-root.css-td54yc:nth-child(11)::after { animation-duration: 13.5s !important; animation-delay: -6.5s !important; }
.MuiBox-root.css-qhetv7:nth-child(11)::after { animation-duration: 35s !important; animation-delay: -25s !important; }


/* 定义各种粒子运动动画 */

/* 1. 直线运动 */
@keyframes particle-move-linear {
  0% { transform: translate(0, 0); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translate(100px, -150px); opacity: 0; }
}

/* 2. 波浪形运动 */
@keyframes particle-move-wave {
  0% { transform: translate(0, 0); opacity: 0; }
  10% { opacity: 1; }
  25% { transform: translate(20px, -30px); }
  50% { transform: translate(-20px, -60px); }
  75% { transform: translate(20px, -90px); }
  90% { opacity: 1; }
  100% { transform: translate(-10px, -120px); opacity: 0; }
}

/* 3. 圆形运动 */
@keyframes particle-move-circle {
  0% { transform: translate(0, 0) rotate(0deg); opacity: 0; }
  10% { opacity: 1; }
  25% { transform: translate(30px, 0px) rotate(90deg); }
  50% { transform: translate(0px, 30px) rotate(180deg); }
  75% { transform: translate(-30px, 0px) rotate(270deg); }
  90% { opacity: 1; }
  100% { transform: translate(0, 0) rotate(360deg); opacity: 0; }
}

/* 4. Z字形运动 */
@keyframes particle-move-zigzag {
  0% { transform: translate(0, 0); opacity: 0; }
  10% { opacity: 1; }
  25% { transform: translate(50px, -20px); }
  50% { transform: translate(0px, -40px); }
  75% { transform: translate(50px, -60px); }
  90% { opacity: 1; }
  100% { transform: translate(0px, -80px); opacity: 0; }
}

/* 5. 螺旋形运动 */
@keyframes particle-move-spiral {
  0% { transform: translate(0, 0) scale(1) rotate(0deg); opacity: 0; }
  10% { opacity: 1; }
  25% { transform: translate(10px, -10px) scale(1.1) rotate(90deg); }
  50% { transform: translate(-10px, -20px) scale(1.2) rotate(180deg); }
  75% { transform: translate(5px, -30px) scale(1.1) rotate(270deg); }
  90% { opacity: 1; }
  100% { transform: translate(0, -40px) scale(1) rotate(360deg); opacity: 0; }
}

/* 6. 向上漂浮并逐渐消失 */
@keyframes particle-move-fade-up {
  0% { transform: translateY(0); opacity: 1; }
  100% { transform: translateY(-100px); opacity: 0; }
}

/* 7. 轻微弹跳运动 */
@keyframes particle-move-bounce {
  0%, 100% { transform: translateY(0); opacity: 1; }
  25% { transform: translateY(-10px); }
  50% { transform: translateY(0); }
  75% { transform: translateY(-5px); }
}

/* 8. 缓慢随机漂移 */
@keyframes particle-move-drift {
  0% { transform: translate(0, 0); opacity: 0; }
  10% { opacity: 1; }
  25% { transform: translate(5px, 8px); }
  50% { transform: translate(-10px, 2px); }
  75% { transform: translate(3px, -7px); }
  90% { opacity: 1; }
  100% { transform: translate(-5px, 10px); opacity: 0; }
}

/* 9. 脉冲式缩放和淡入淡出 */
@keyframes particle-move-pulse {
  0%, 100% { transform: scale(1); opacity: 0; }
  50% { transform: scale(1.5); opacity: 1; }
}

/* 10. 围绕一个点进行椭圆或圆形轨道运动 (模拟，实际是伪元素整体移动) */
@keyframes particle-move-orbit {
  0% { transform: translate(0, 0) rotate(0deg); opacity: 0; }
  10% { opacity: 1; }
  25% { transform: translate(50px, 20px) rotate(90deg); }
  50% { transform: translate(0px, 40px) rotate(180deg); }
  75% { transform: translate(-50px, 20px) rotate(270deg); }
  90% { opacity: 1; }
  100% { transform: translate(0, 0) rotate(360deg); opacity: 0; }
}


/* 左侧导航激活状态 - 赛博线条特效 */
.MuiListItemButton-root.Mui-selected::before,
.MuiListItemButton-root[aria-selected="true"]::before,
.MuiListItemButton-root.active::before {
opacity: 1 !important;
animation: cyberpunk-border-flow 4s linear infinite, cyberpunk-border-opacity-pulse 2s ease-in-out infinite !important;
}


/* 激活悬停效果 - 排除已激活的导航项 */
.MuiBox-root .css-1ow8u3y:hover, .css-1rgmi2n:hover, .css-bjjbb7:hover, .css-hds0vx:hover,
.css-aafiep:hover, .css-xd8r7u:hover, .css-ya2z3b:hover, .css-8sla8j:hover, .css-ulr2qx:hover, .css-17rlh6j:hover,
.main .bg-primary-foreground:hover, .side .bg-primary-foreground:hover,
.main .bg-foreground:hover, .side .bg-foreground:hover,
.main .bg-content1:hover, .side .bg-content1:hover,
.main .bg-default:hover, .side .bg-default:hover,
.MuiButton-root:hover,
.MuiListItemButton-root:hover:not(.Mui-selected):not([aria-selected="true"]):not(.active) {
transform: translateY(-4px) !important;
}

/* 悬停时卡片背景透明度变化 */
.MuiBox-root .css-1ow8u3y:hover, .css-1rgmi2n:hover, .css-bjjbb7:hover, .css-hds0vx:hover,
.css-aafiep:hover, .css-xd8r7u:hover, .css-ya2z3b:hover, .css-8sla8j:hover, .css-ulr2qx:hover, .css-17rlh6j:hover {
background-color: var(--card-bg-light-hover) !important;
}
@media (prefers-color-scheme: dark) {
.MuiBox-root .css-1ow8u3y:hover, .css-1rgmi2n:hover, .css-bjjbb7:hover, .css-hds0vx:hover,
.css-aafiep:hover, .css-xd8r7u:hover, .css-ya2z3b:hover, .css-8sla8j:hover, .css-ulr2qx:hover, .css-17rlh6j:hover {
  background-color: var(--card-bg-dark-hover) !important;
}
}

/* 悬停时激活边框并启动透明度脉冲动画 - 排除已激活的导航项 */
.MuiBox-root .css-1ow8u3y:hover::before, .css-1rgmi2n:hover::before, .css-bjjbb7:hover::before, .css-hds0vx:hover::before,
.css-aafiep:hover::before, .css-xd8r7u:hover::before, .css-ya2z3b:hover::before, .css-8sla8j:hover::before, .css-ulr2qx:hover::before, .css-17rlh6j:hover::before,
.main .bg-primary-foreground:hover::before, .side .bg-primary-foreground:hover::before,
.main .bg-foreground:hover::before, .side .bg-foreground::before,
.main .bg-content1:hover::before, .side .bg-content1::before,
.main .bg-default:hover::before, .side .bg-default::before,
.MuiButton-root:hover::before,
.MuiListItemButton-root:hover:not(.Mui-selected):not([aria-selected="true"]):not(.active)::before {
opacity: 1 !important;
animation: cyberpunk-border-flow 4s linear infinite, cyberpunk-border-opacity-pulse 2s ease-in-out infinite !important;
}


/* 导航项目整体盒子*/
.css-dh9epo{
min-width:280px;
}

/* 导航项目样式优化 */
.MuiListItem-root .MuiListItemButton-root {
width: var(--nav-item-width) !important;
border-radius: var(--nav-item-border-radius) !important;
background-color: transparent !important;
margin: 4px 0 !important;
transition: all 0.3s ease !important;
display: flex !important;
align-items: center !important;
justify-content: center !important;
}

/* 导航项目文字水平居中 */
.MuiListItem-root .MuiListItemButton-root .MuiListItemText-root {
display: flex !important;
align-items: center !important;
}

/* ========================================
 Gemini 风格渐变色和悬停效果
 ======================================== */

/* Gemini 风格 CSS 变量定义 */
:root {
--gemini-color-logo-gradient: linear-gradient(90deg,
    #2079fe 0%,
    #098efb 33.53%,
    #ad89eb 70%,
    #ef4e5e 100%);
--gemini-color-white: #ffffff;
--gemini-color-black: #000000;
--gemini-color-grey-50: #f8f9fa;
--gemini-color-grey-100: #f1f3f4;
--gemini-color-grey-200: #e8eaed;
--gemini-color-grey-300: #dadce0;
--gemini-color-grey-400: #bdc1c6;
--gemini-color-grey-500: #9aa0a6;
--gemini-color-grey-600: #80868b;
--gemini-color-grey-700: #5f6368;
--gemini-color-grey-800: #3c4043;
--gemini-color-grey-900: #202124;
--gemini-color-blue-50: #e8f0fe;
--gemini-color-blue-100: #d2e3fc;
--gemini-color-blue-200: #aecbfa;
--gemini-color-blue-300: #8ab4f8;
--gemini-color-blue-400: #669df6;
--gemini-color-blue-500: #4285f4;
--gemini-color-blue-600: #1a73e8;
--gemini-color-blue-700: #1967d2;
--gemini-color-blue-800: #185abc;
--gemini-color-blue-900: #174ea6;
--gemini-color-red-50: #fce8e6;
--gemini-color-red-100: #fad2cf;
--gemini-color-red-200: #f6aea9;
--gemini-color-red-300: #f28b82;
--gemini-color-red-400: #ee675c;
--gemini-color-red-500: #ea4335;
--gemini-color-red-600: #d93025;
--gemini-color-red-700: #c5221f;
--gemini-color-red-800: #b31412;
--gemini-color-red-900: #a50e0e;
--gemini-color-green-50: #e6f4ea;
--gemini-color-green-100: #ceead6;
--gemini-color-green-200: #a8dab5;
--gemini-color-green-300: #81c995;
--gemini-color-green-400: #5bb974;
--gemini-color-green-500: #34a853;
--gemini-color-green-600: #137333;
--gemini-color-green-700: #0d652d;
--gemini-color-green-800: #0b5394;
--gemini-color-green-900: #0a5d00;
--gemini-color-yellow-50: #fef7e0;
--gemini-color-yellow-100: #feefc3;
--gemini-color-yellow-200: #fde047;
--gemini-color-yellow-300: #fcd34d;
--gemini-color-yellow-400: #fbbf24;
--gemini-color-yellow-500: #f59e0b;
--gemini-color-yellow-600: #d97706;
--gemini-color-yellow-700: #b45309;
--gemini-color-yellow-800: #92400e;
--gemini-color-yellow-900: #78350f;
--gemini-color-gemini-blue: #368efe;
--gemini-color-gemini-cyan: #4fabff;
--gemini-color-gemini-light-blue: #b1c5ff;
--gemini-color-blue: #368efe;
--gemini-color-purple-100: #ac87eb;
--gemini-color-red-200: #ee4d5d;
--gemini-color-green-800: #137333;
--gemini-color-blue-800: #185ABC;
--gemini-color-blue-gradient: linear-gradient(61deg, #64b8fb 6.28%, #217bfe 76.97%);
--gemini-color-pink-gradient: linear-gradient(90deg, #a485fa -104.88%, var(--gemini-color-red-200) 198.78%);
--gemini-color-logo-gradient: linear-gradient(90deg, #217bfe 0%, #078efb 33.53%, #ac87eb 70%, #ee4d5d 100%);
--gemini-color-primary-button-gradient: linear-gradient(52deg, #0844ff 11.5%, #64b8fb 129.52%);
--gemini-color-chart-gradient: linear-gradient(105deg, #446eff 18.71%, #2e96ff 49.8%, #b1c5ff 90.55%);
--gemini-color-foreground: var(--gemini-color-white);
--gemini-color-background: var(--gemini-color-grey-900);
--gemini-branding-button-gradient: linear-gradient(15deg, #217BFE 1.02%, #078EFB 28.51%, #A190FF 80.14%, #BD99FE 102.85%);
--gemini-branding-text-gradient: linear-gradient(90deg, #217BFE 0%, #078EFB 33.53%, #AC87EB 67.74%, #EE4D5D 100%);
--gemini-enhanced-text-gradient: linear-gradient(90deg, #1a5fd1 0%, #0670c7 33.53%, #8b6bc2 67.74%, #c73e4e 100%);
--gemini-gradient-linear-colors: var(--gemini-color-gemini-blue) 5.96%, var(--gemini-color-gemini-cyan) 56.89%, var(--gemini-color-gemini-light-blue) 93.53%;
--gemini-gradient-linear: linear-gradient(53deg, #0260FF 9.29%, #40A2FF 48.23%, #A8BEFF 82.56%);
--gemini-text-gradient-light-blue: linear-gradient(69deg, #AABDF4 16.42%, #FFF 77.56%, #A8BEFF 124.91%);

/* 平滑渐变色彩 - 更自然的过渡 */
--smooth-gradient: linear-gradient(90deg,
  #669df6 0%,   /* Blue */
  #7e57c2 25%,  /* Indigo */
  #ab47bc 50%,  /* Purple */
  #ec407a 75%,  /* Pink */
  #ef5350 100%  /* Red */
);

/* 平滑流动渐变 - 更长的过渡距离 */
--smooth-flowing-gradient: linear-gradient(90deg,
  #669df6, #7e57c2, #ab47bc, #ec407a, #ef5350, #ec407a, #ab47bc, #7e57c2, #669df6
);
}


/* 左侧导航文字渐变效果 - 使用平滑渐变 */
.MuiListItemText-primary {
background-image: var(--smooth-gradient) !important;
background-clip: text !important;
-webkit-background-clip: text !important;
-webkit-text-fill-color: transparent !important;
font-weight: bold !important;
background-size: 200% auto !important;
animation: text-gradient-flow 8s linear infinite !important;
left: 50px !important;
width: 100% !important;
}


/* 文字渐变流动动画 */
@keyframes text-gradient-flow {
0% {
  background-position: 0% center !important;
}
100% {
  background-position: 200% center !important;
}
}

/* SVG logo震撼渐变效果 - 多彩流动渐变 */
#layout1 {
position: relative !important;
overflow: hidden !important;
}

/* 为SVG添加动态渐变背景 */
#layout1::before {
content: '' !important;
position: absolute !important;
top: -50% !important;
left: -50% !important;
width: 200% !important;
height: 200% !important;
background: linear-gradient(
  45deg,
  #4285f4 0%,
  #ea4335 15%,
  #fbbc04 30%,
  #34a853 45%,
  #4285f4 60%,
  #ea4335 75%,
  #fbbc04 90%,
  #34a853 100%
) !important;
background-size: 400% 400% !important;
animation: logo-gradient-flow 6s ease-in-out infinite !important;
z-index: -1 !important;
border-radius: 50% !important;
filter: blur(8px) !important;
}

/* SVG路径的震撼效果 */
#layout1 .st1 {
fill: #e22acd !important;

}

/* 震撼的渐变流动动画 */
@keyframes logo-gradient-flow {
0% {
  background-position: 0% 50% !important;
  transform: rotate(0deg) scale(1) !important;
}
25% {
  background-position: 100% 50% !important;
  transform: rotate(90deg) scale(1.1) !important;
}
50% {
  background-position: 100% 100% !important;
  transform: rotate(180deg) scale(1.2) !important;
}
75% {
  background-position: 0% 100% !important;
  transform: rotate(270deg) scale(1.1) !important;
}
100% {
  transform: rotate(360deg) scale(1) !important;
}
}

/* 脉冲效果 */
@keyframes logo-pulse {
0% {
  filter: drop-shadow(0 0 8px rgba(66, 133, 244, 0.6))
          drop-shadow(0 0 16px rgba(234, 67, 53, 0.4))
          drop-shadow(0 0 24px rgba(251, 188, 4, 0.3)) !important;
}
100% {
  filter: drop-shadow(0 0 12px rgba(66, 133, 244, 0.8))
          drop-shadow(0 0 24px rgba(234, 67, 53, 0.6))
          drop-shadow(0 0 36px rgba(251, 188, 4, 0.5)) !important;
}
}

/* 颜色变换动画 */
@keyframes logo-color-shift {
0% { fill: #4285f4 !important; }
25% { fill: #ea4335 !important; }
50% { fill: #fbbc04 !important; }
75% { fill: #34a853 !important; }
100% { fill: #4285f4 !important; }
}

/* data-tauri-drag-region 元素文字渐变效果 */
.css-1l0zim6 {
background-image: var(--smooth-gradient) !important;
background-clip: text !important;
-webkit-background-clip: text !important;
-webkit-text-fill-color: transparent !important;
color: transparent !important;
font-weight: bold !important;
}

/* 右侧设置页面文字渐变效果 - 只针对文字，不影响图标 */
.css-1i24pk4 span:not([class*="MuiSvgIcon"]):not([class*="Icon"]) {
background-image: var(--smooth-gradient) !important;
background-clip: text !important;
-webkit-background-clip: text !important;
-webkit-text-fill-color: transparent !important;
color: transparent !important;
font-weight: bold !important;
font-size: 20px;
}

/* 右侧所有文字渐变效果 - 覆盖更多元素 */
.main span,
.side span,
.main .MuiTypography-root,
.side .MuiTypography-root,
.main .text-sm,
.side .text-sm,
.main .text-base,
.side .text-base,
.main .text-lg,
.side .text-lg,
.main .text-xl,
.side .text-xl,
.main .font-medium,
.side .font-medium,
.main .font-semibold,
.side .font-semibold,
.main .leading-6,
.side .leading-6,
.main .leading-7,
.side .leading-7,
.main .leading-8,
.side .leading-8 {
background-image: var(--smooth-gradient) !important;
background-clip: text !important;
-webkit-background-clip: text !important;
-webkit-text-fill-color: transparent !important;
font-weight: bold !important;
background-size: 200% auto !important;
animation: text-gradient-flow 8s linear infinite !important;
}

/* 赛博朋克边框旋转动画 */
@keyframes cyberpunk-border-flow {
to {
  --cyber-border-angle: 360deg;
}
}

/* 新增：赛博朋克边框透明度脉冲动画 */
@keyframes cyberpunk-border-opacity-pulse {
0%, 100% { opacity: 0.6; }
50% { opacity: 1; }
}


h6 {
font-weight: bold !important;
background: linear-gradient(90deg,
  #ff0000 0%,   /* 红 */
  #ff3300 8%,   /* 红橙 */
  #ff6600 16%,  /* 橙 */
  #ff9900 24%,  /* 深橙 */
  #ffcc00 32%,  /* 金橙 */
  #ffff00 40%,  /* 黄 */
  #ccff00 48%,  /* 黄绿 */
  #66ff00 56%,  /* 绿黄 */
  #00ff00 64%,  /* 绿 */
  #00ff66 72%,  /* 绿青 */
  #00ffcc 80%,  /* 青绿 */
  #00ccff 88%,  /* 青 */
  #0066ff 96%,  /* 蓝 */
  #ff0000 100%  /* 回到红色形成循环 */
) !important;
background-size: 400% auto !important;
-webkit-background-clip: text !important;
background-clip: text !important;
-webkit-text-fill-color: transparent !important;
animation: rainbow-flow 8s ease-in-out infinite !important;
position: relative !important;
z-index: 1 !important;
/* 恢复文字选择功能 */
user-select: text !important;
-webkit-user-select: text !important;
-moz-user-select: text !important;
}

/* 修复h6和其他文字选择问题 */
h6::selection {
background: rgba(0, 123, 255, 0.3) !important;
color: #ffffff !important;
}

h6::-moz-selection {
background: rgba(0, 123, 255, 0.3) !important;
color: #ffffff !important;
}

/* 修复 .css-1u0w142 文字选择问题 */
.css-1u0w142 {
user-select: text !important;
-webkit-user-select: text !important;
-moz-user-select: text !important;
}

.css-1u0w142::selection {
background: rgba(0, 123, 255, 0.3) !important;
color: #ffffff !important;
}

.css-1u0w142::-moz-selection {
background: rgba(0, 123, 255, 0.3) !important;
color: #ffffff !important;
}



@keyframes rainbow-flow {
0% {
  background-position: 0% center;
  filter: brightness(1) saturate(1);
}
25% {
  background-position: 100% center;
  filter: brightness(1.1) saturate(1.2);
}
50% {
  background-position: 200% center;
  filter: brightness(0.9) saturate(0.8);
}
75% {
  background-position: 300% center;
  filter: brightness(1.2) saturate(1.3);
}
100% {
  background-position: 400% center;
  filter: brightness(1) saturate(1);
}
}