/* ========================================
   Clash Verge Rev 优化主题 - 浅色/深色双模式
   ======================================== */


   完全重新编写卡片内粒子的效果，要求如下：
   所有粒子有同样的大小。
   设置一个粒子的速度区间，每一个粒子有不同的速度大小，使用至少20种不同的粒子速度。
   每一个粒子出生在卡片内的任意一个位置，每个粒子的出生位置都不相同，在卡片内使用至少50种不同的粒子出生点位。
   粒子数量适中，不多也不少。
   每一个粒子的运动轨迹都是不同的，有的朝一个方向移动，有的沿着弧形移动，有的像蛇型移动，有的旋转移动等等，使用至少十种运动轨迹，以及完全随机的向任意方向移动。
   每一个粒子的颜色种类足够丰富，达到至少30种不同颜色，并尽量取颜色鲜艳的色域（不取淡色灰色这些不鲜艳的色域）。
   动画的任意时间都有任意独立的粒子，而不存在时间不同会有周期不同的粒子。
最后优化性能，检测所有变量以及选择器是否真的在工作，没有正确工作的直接删除。


section {
    background-color: rgba(255, 255, 255, 0) !important;
}


html {
  font-size: 22px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  --background-color: #f0f0f000 !important;
}

.base-container {
    background-color: #f0f0f000 !important;
}


.MuiTypography-root {
    background-color: rgba(0, 0, 0, 0) !important;
}


/* MuiBox 基础样式 - 全局透明背景 */
.MuiBox-root {
    background-color: rgba(255, 255, 255, 0) !important;
}


/* 全局背景图 - 透明度0.8 */
.css-1li7dvq {
    position: relative;
    background-color: rgba(255, 255, 255, 0) !important;
}

.css-1li7dvq::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('https://img.paulzzh.com/touhou/random?site=all&size=pc') !important;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 1;
    z-index: -2;
}

/*浅色背景底色*/
.css-1li7dvq::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: -1;
}

.css-l3ykv8 {
    position: relative;
    background-color: rgba(255, 255, 255, 0) !important;
    color: rgb(239, 239, 239) !important;
}

.css-l3ykv8::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('https://img.paulzzh.com/touhou/random?site=all&size=pc') !important;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 1;
    z-index: -2;
}

/*深色背景底色*/
.css-l3ykv8::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: -1;
}


/* 主题模式和变量定义 */
:root {
  /* 浅色模式变量 */

  --border-light: rgba(0, 0, 0, 0.08);
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.2);
  
  
  /* 布局变量 */
  --base-spacing: 1.5rem;
  --grid-gap: 1.2rem;
  --card-min-width: 280px;
  --sidebar-width: 280px;
  --header-height: 64px;
  --border-radius: 12px;
  --border-radius-small: 8px;
  --border-radius-large: 16px;
  --nav-item-width: 220px;
  --nav-item-border-radius: 30px;
  --card-bg-light: rgba(255, 255, 255, 0.85);
  --card-bg-dark: rgba(33, 33, 33, 0.85);
}

/* 深色模式变量 */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme="light"]):not(.light-mode) {

    --text-secondary: rgba(255, 255, 255, 0.9);
    --text-tertiary: rgba(255, 255, 255, 0.7);
    --text-inverse: rgba(33, 37, 41, 0.95);

    --border-light: rgba(255, 255, 255, 0.08);

    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.3);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.4);
    --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.5);

    /* 深色模式下的渐变色 */
    --smooth-gradient: linear-gradient(90deg,
      #8ab4f8 0%,   /* Lighter Blue */
      #9575cd 25%,  /* Lighter Indigo */
      #ba68c8 50%,  /* Lighter Purple */
      #f06292 75%,  /* Lighter Pink */
      #e57373 100%  /* Lighter Red */
    );
    --smooth-flowing-gradient: linear-gradient(90deg,
      #8ab4f8, #9575cd, #ba68c8, #f06292, #e57373, #f06292, #ba68c8, #9575cd, #8ab4f8
    );
  }
}


/* 左侧导航栏 */
.layout__left{
  flex: 0 0 var(--sidebar-width);
  background: var(--background-glass);
  backdrop-filter: var(--blur-medium);
  border-right: 1px solid var(--border-light);
  padding: var(--base-spacing);
  min-width: 360px;
  max-width: 420px;
  box-shadow: var(--shadow-light);
  position: relative;
  z-index: 10;
}


/* ========================================
   响应式网格系统
   ======================================== */

/* 响应式网格容器 */
.responsive-grid, .grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(var(--card-min-width), 1fr));
  gap: var(--grid-gap);
  padding: var(--grid-gap);
  width: 100%;
  align-items: start;
}

/* 网格项目 */
.grid-item {
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* 菜单系统网格 */
.the-menu, .menu-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--base-spacing);
  margin: var(--base-spacing) 0;
}


/* ========================================
   Material-UI 组件优化
   ======================================== */



/* 按钮组件 */
.MuiButton-root, .MuiButtonBase-root {
  border: 1px solid var(--border-light) !important;
  backdrop-filter: var(--blur-light);
  border-radius: var(--border-radius-small) !important;

  box-shadow: var(--shadow-light) !important;
}

/*订阅卡片格式*/
.MuiBox-root .css-1ow8u3y, .css-1rgmi2n, .css-bjjbb7 ,.css-hds0vx{
  background-color: var(--card-bg-light) !important;
  border-radius: var(--border-radius) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

/* 深色模式下的订阅卡片 */
@media (prefers-color-scheme: dark) {
  .MuiBox-root .css-1ow8u3y, .css-1rgmi2n, .css-bjjbb7 ,.css-hds0vx{
    background-color: var(--card-bg-dark) !important;
  }
}

/* 流媒体测试卡片格式 */
.css-aafiep, .css-xd8r7u, .css-ya2z3b, .css-8sla8j {
  background-color: var(--card-bg-light) !important;
  border-radius: var(--border-radius) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

/* 深色模式下的流媒体测试卡片 */
@media (prefers-color-scheme: dark) {
  .css-aafiep, .css-xd8r7u, .css-ya2z3b, .css-8sla8j {
    background-color: var(--card-bg-dark) !important;
  }
}

/* 统一的卡片悬停动效 - 动态渐变波浪 */
/* 为卡片鼠标悬停增加出色的动态渐变色特效，并且让卡片鼠标悬停的渐变色特效流动起来，向波浪一样往四周流动的特效 */
.MuiBox-root .css-1ow8u3y:hover, .css-1rgmi2n:hover, .css-bjjbb7:hover, .css-hds0vx:hover,
.css-aafiep:hover, .css-xd8r7u:hover, .css-ya2z3b:hover, .css-8sla8j:hover,
.MuiListItemButton-root:hover {
  /* 使用径向渐变创造波浪感，并确保颜色平滑过渡 */
  background: radial-gradient(circle, 
    rgba(138, 180, 248, 0.3), /* Lighter Blue */
    rgba(149, 117, 205, 0.3), /* Lighter Indigo */
    rgba(186, 104, 200, 0.3), /* Lighter Purple */
    rgba(240, 98, 146, 0.3), /* Lighter Pink */
    rgba(229, 115, 115, 0.3), /* Lighter Red */
    rgba(240, 98, 146, 0.3), /* Lighter Pink */
    rgba(186, 104, 200, 0.3), /* Lighter Purple */
    rgba(149, 117, 205, 0.3), /* Lighter Indigo */
    rgba(138, 180, 248, 0.3)  /* Lighter Blue */
  ) !important;
  background-size: 300% 300% !important; /* 放大背景以实现平滑移动 */
  animation: card-wave-spread 8s ease-in-out infinite !important;
  transform: none !important; /* 增强立体感 */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05), 0 0 0 0.5px rgba(255, 255, 255, 0.01) !important; /* 增强阴影 */
  transition: all 0.3s ease-out !important;
}

/* 导航项目整体盒子*/
.css-dh9epo{
  min-width:280px;
}

/* 导航项目样式优化 */
.MuiListItem-root .MuiListItemButton-root {
  width: var(--nav-item-width) !important;
  border-radius: var(--nav-item-border-radius) !important;
  margin: 4px 0 !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 导航项目文字水平居中 */
.MuiListItem-root .MuiListItemButton-root .MuiListItemText-root {
  display: flex !important;
  align-items: center !important;
}

/* ========================================
   Gemini 风格渐变色和悬停效果
   ======================================== */

/* Gemini 风格 CSS 变量定义 */
:root {
  --gemini-color-logo-gradient: linear-gradient(90deg,
      #2079fe 0%,
      #098efb 33.53%,
      #ad89eb 70%,
      #ef4e5e 100%);
  --gemini-color-white: #ffffff;
  --gemini-color-black: #000000;
  --gemini-color-grey-50: #f8f9fa;
  --gemini-color-grey-100: #f1f3f4;
  --gemini-color-grey-200: #e8eaed;
  --gemini-color-grey-300: #dadce0;
  --gemini-color-grey-400: #bdc1c6;
  --gemini-color-grey-500: #9aa0a6;
  --gemini-color-grey-600: #80868b;
  --gemini-color-grey-700: #5f6368;
  --gemini-color-grey-800: #3c4043;
  --gemini-color-grey-900: #202124;
  --gemini-color-blue-50: #e8f0fe;
  --gemini-color-blue-100: #d2e3fc;
  --gemini-color-blue-200: #aecbfa;
  --gemini-color-blue-300: #8ab4f8;
  --gemini-color-blue-400: #669df6;
  --gemini-color-blue-500: #4285f4;
  --gemini-color-blue-600: #1a73e8;
  --gemini-color-blue-700: #1967d2;
  --gemini-color-blue-800: #185abc;
  --gemini-color-blue-900: #174ea6;
  --gemini-color-red-50: #fce8e6;
  --gemini-color-red-100: #fad2cf;
  --gemini-color-red-200: #f6aea9;
  --gemini-color-red-300: #f28b82;
  --gemini-color-red-400: #ee675c;
  --gemini-color-red-500: #ea4335;
  --gemini-color-red-600: #d93025;
  --gemini-color-red-700: #c5221f;
  --gemini-color-red-800: #b31412;
  --gemini-color-red-900: #a50e0e;
  --gemini-color-green-50: #e6f4ea;
  --gemini-color-green-100: #ceead6;
  --gemini-color-green-200: #a8dab5;
  --gemini-color-green-300: #81c995;
  --gemini-color-green-400: #5bb974;
  --gemini-color-green-500: #34a853;
  --gemini-color-green-600: #137333;
  --gemini-color-green-700: #0d652d;
  --gemini-color-green-800: #0b5394;
  --gemini-color-green-900: #0a5d00;
  --gemini-color-yellow-50: #fef7e0;
  --gemini-color-yellow-100: #feefc3;
  --gemini-color-yellow-200: #fde047;
  --gemini-color-yellow-300: #fcd34d;
  --gemini-color-yellow-400: #fbbf24;
  --gemini-color-yellow-500: #f59e0b;
  --gemini-color-yellow-600: #d97706;
  --gemini-color-yellow-700: #b45309;
  --gemini-color-yellow-800: #92400e;
  --gemini-color-yellow-900: #78350f;
  --gemini-color-gemini-blue: #368efe;
  --gemini-color-gemini-cyan: #4fabff;
  --gemini-color-gemini-light-blue: #b1c5ff;
  --gemini-color-blue: #368efe;
  --gemini-color-purple-100: #ac87eb;
  --gemini-color-red-200: #ee4d5d;
  --gemini-color-green-800: #137333;
  --gemini-color-blue-800: #185ABC;
  --gemini-color-blue-gradient: linear-gradient(61deg, #64b8fb 6.28%, #217bfe 76.97%);
  --gemini-color-pink-gradient: linear-gradient(90deg, #a485fa -104.88%, var(--gemini-color-red-200) 198.78%);
  --gemini-color-logo-gradient: linear-gradient(90deg, #217bfe 0%, #078efb 33.53%, #ac87eb 70%, #ee4d5d 100%);
  --gemini-color-primary-button-gradient: linear-gradient(52deg, #0844ff 11.5%, #64b8fb 129.52%);
  --gemini-color-chart-gradient: linear-gradient(105deg, #446eff 18.71%, #2e96ff 49.8%, #b1c5ff 90.55%);
  --gemini-color-foreground: var(--gemini-color-white);
  --gemini-color-background: var(--gemini-color-grey-900);
  --gemini-branding-button-gradient: linear-gradient(15deg, #217BFE 1.02%, #078EFB 28.51%, #A190FF 80.14%, #BD99FE 102.85%);
  --gemini-branding-text-gradient: linear-gradient(90deg, #217BFE 0%, #078EFB 33.53%, #AC87EB 67.74%, #EE4D5D 100%);
  --gemini-enhanced-text-gradient: linear-gradient(90deg, #1a5fd1 0%, #0670c7 33.53%, #8b6bc2 67.74%, #c73e4e 100%);
  --gemini-gradient-linear-colors: var(--gemini-color-gemini-blue) 5.96%, var(--gemini-color-gemini-cyan) 56.89%, var(--gemini-color-gemini-light-blue) 93.53%;
  --gemini-gradient-linear: linear-gradient(53deg, #0260FF 9.29%, #40A2FF 48.23%, #A8BEFF 82.56%);
  --gemini-text-gradient-light-blue: linear-gradient(69deg, #AABDF4 16.42%, #FFF 77.56%, #A8BEFF 124.91%);

  /* 平滑渐变色彩 - 更自然的过渡 */
  --smooth-gradient: linear-gradient(90deg,
    #669df6 0%,   /* Blue */
    #7e57c2 25%,  /* Indigo */
    #ab47bc 50%,  /* Purple */
    #ec407a 75%,  /* Pink */
    #ef5350 100%  /* Red */
  );

  /* 平滑流动渐变 - 更长的过渡距离 */
  --smooth-flowing-gradient: linear-gradient(90deg,
    #669df6, #7e57c2, #ab47bc, #ec407a, #ef5350, #ec407a, #ab47bc, #7e57c2, #669df6
  );
}


/* 左侧导航文字渐变效果 - 使用平滑渐变 */
.MuiListItemText-primary {
  background-image: var(--smooth-gradient) !important;
  background-clip: text !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  font-weight: bold !important;
  background-size: 200% auto !important;
  animation: text-gradient-flow 8s linear infinite !important;
  left: 50px !important;
  width: 100% !important;
}


/* 文字渐变流动动画 */
@keyframes text-gradient-flow {
  0% {
    background-position: 0% center !important;
  }
  100% {
    background-position: 200% center !important;
  }
}

/* data-tauri-drag-region 元素文字渐变效果 */
.css-1l0zim6 {
  background-image: var(--smooth-gradient) !important;
  background-clip: text !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  color: transparent !important;
  font-weight: bold !important;
}

/* 右侧设置页面文字渐变效果 - 只针对文字，不影响图标 */
.css-1i24pk4 span:not([class*="MuiSvgIcon"]):not([class*="Icon"]) {
  background-image: var(--smooth-gradient) !important;
  background-clip: text !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  color: transparent !important;
  font-weight: bold !important;
  font-size: 20px;
}

/* 右侧所有文字渐变效果 - 覆盖更多元素 */
.main span,
.side span,
.main .MuiTypography-root,
.side .MuiTypography-root,
.main .text-sm,
.side .text-sm,
.main .text-base,
.side .text-base,
.main .text-lg,
.side .text-lg,
.main .text-xl,
.side .text-xl,
.main .font-medium,
.side .font-medium,
.main .font-semibold,
.side .font-semibold,
.main .leading-6,
.side .leading-6,
.main .leading-7,
.side .leading-7,
.main .leading-8,
.side .leading-8 {
  background-image: var(--smooth-gradient) !important;
  background-clip: text !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  font-weight: bold !important;
  background-size: 200% auto !important;
  animation: text-gradient-flow 8s linear infinite !important;
}

/* 卡片波浪流动动画 */
@keyframes card-wave-spread {
  0% {
    background-position: 50% 50%;
    background-size: 300% 300%;
  }
  50% {
    background-position: 0% 0%;
    background-size: 500% 500%;
  }
  100% {
    background-position: 50% 50%;
    background-size: 300% 300%;
  }
}

svg#layout1 g path.st1:nth-of-type(1) { fill: #a8dadc; } /* Light Teal */
svg#layout1 g path.st1:nth-of-type(2) { fill: #fcf5b0; } /* Pale Yellow */
svg#layout1 g path.st1:nth-of-type(3) { fill: #e6beae; } /* Light Coral */
svg#layout1 g path.st1:nth-of-type(4) { fill: #c7d8ed; } /* Light Blue */
svg#layout1 g path.st1:nth-of-type(5) { fill: #d4a5a5; } /* Dusty Rose */
svg#layout1 g path.st1:nth-of-type(6) { fill: #a7d9b9; } /* Mint Green */
svg#layout1 g path.st1:nth-of-type(7) { fill: #e0b1cb; } /* Light Purple */
svg#layout1 g path.st1:nth-of-type(8) { fill: #f8d568; } /* Pale Gold */
svg#layout1 g path.st1:nth-of-type(9) { fill: #b0c4de; } /* Light Steel Blue */
svg#layout1 g path.st1:nth-of-type(10) { fill: #f7cac9; } /* Light Pink */