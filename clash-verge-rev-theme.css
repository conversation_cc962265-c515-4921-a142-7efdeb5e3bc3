/*



*/
section {
  background-color: rgba(255, 255, 255, 0) !important;
}


html {
font-size: 22px;
-webkit-font-smoothing: antialiased;
-moz-osx-font-smoothing: grayscale;
--background-color: #f0f0f000 !important;
}

.base-container {
  background-color: #f0f0f000 !important;
}


.MuiTypography-root {
  background-color: rgba(0, 0, 0, 0) !important;
}


/* MuiBox 基础样式 - 全局透明背景 */
.MuiBox-root {
  background-color: rgba(255, 255, 255, 0) !important;
}


/* 全局背景图 - 透明度0.8 */
.css-1li7dvq {
  position: relative;
  background-color: rgba(255, 255, 255, 0) !important;
}

.css-1li7dvq::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('https://img.paulzzh.com/touhou/random?site=all&size=pc') !important;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 1;
  z-index: -2;
}

/*浅色背景底色*/
.css-1li7dvq::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: -1;
}

.css-l3ykv8 {
  position: relative;
  background-color: rgba(255, 255, 255, 0) !important;
  color: rgb(239, 239, 239) !important;
}

.css-l3ykv8::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('https://img.paulzzh.com/touhou/random?site=all&size=pc') !important;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 1;
  z-index: -2;
}

/*深色背景底色*/
.css-l3ykv8::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: -1;
}


/* 主题模式和变量定义 */
:root {
/* 浅色模式变量 */

--border-light: rgba(0, 0, 0, 0.08);
--shadow-light: 0 2px 8px rgba(0, 0, 0, 0.3);
--shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
--shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.2);


/* 布局变量 */
--base-spacing: 1.5rem;
--grid-gap: 1.2rem;
--card-min-width: 280px;
--sidebar-width: 280px;
--header-height: 64px;
--border-radius: 12px;
--border-radius-small: 8px;
--border-radius-large: 16px;
--nav-item-width: 220px;
--nav-item-border-radius: 30px;
--card-bg-light: rgba(255, 255, 255, 0.85);
--card-bg-dark: rgba(33, 33, 33, 0.85);
--card-bg-light-hover: rgba(255, 255, 255, 0.05);
--card-bg-dark-hover: rgba(33, 33, 33, 0.05);
}

/* 深色模式变量 */
@media (prefers-color-scheme: dark) {
:root:not([data-theme="light"]):not(.light-mode) {

  --text-secondary: rgba(255, 255, 255, 0.9);
  --text-tertiary: rgba(255, 255, 255, 0.7);
  --text-inverse: rgba(33, 37, 41, 0.95);

  --border-light: rgba(255, 255, 255, 0.08);

  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.5);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.4);
  --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.5);


}
}


/* 左侧导航栏 */
.layout__left{
flex: 0 0 var(--sidebar-width);
background: var(--background-glass);
backdrop-filter: var(--blur-medium);
border-right: 1px solid var(--border-light);
padding: var(--base-spacing);
min-width: 360px;
max-width: 420px;
box-shadow: var(--shadow-light);
position: relative;
z-index: 10;
}


/* ========================================
 响应式网格系统
 ======================================== */

/* 响应式网格容器 */
.responsive-grid, .grid-container {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(var(--card-min-width), 1fr));
gap: var(--grid-gap);
padding: var(--grid-gap);
width: 100%;
align-items: start;
}

/* 网格项目 */
.grid-item {
display: flex;
flex-direction: column;
min-height: 0;
}

/* 菜单系统网格 */
.the-menu, .menu-grid {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
gap: var(--base-spacing);
margin: var(--base-spacing) 0;
}


/* ========================================
 Material-UI 组件优化
 ======================================== */

/* 按钮组件 */
.MuiButton-root, .MuiButtonBase-root {
border: 1px solid var(--border-light) !important;
backdrop-filter: var(--blur-light);
border-radius: var(--border-radius-small) !important;

box-shadow: var(--shadow-light) !important;
}

/* ========================================
 赛博朋克卡片悬停特效
 ======================================== */

/* 定义赛博朋克风格的颜色和动画变量 */
:root {
--cyber-c1: #00fffc; /* 亮青色 */
--cyber-c2: #ff00ff; /* 品红色 */
--cyber-c3: #faff00; /* 亮黄色 */
--cyber-border-angle: 0deg;
/* 革命性粒子颜色系统 - 100种颜色，11个核心粒子 */
--cyber-p1: #ff0000; --cyber-p2: #ff1a00; --cyber-p3: #ff3300; --cyber-p4: #ff4d00; --cyber-p5: #ff6600;
--cyber-p6: #ff8000; --cyber-p7: #ff9900; --cyber-p8: #ffb300; --cyber-p9: #ffcc00; --cyber-p10: #ffe600;
--cyber-p11: #ffff00; --cyber-p12: #e6ff00; --cyber-p13: #ccff00; --cyber-p14: #b3ff00; --cyber-p15: #99ff00;
--cyber-p16: #80ff00; --cyber-p17: #66ff00; --cyber-p18: #4dff00; --cyber-p19: #33ff00; --cyber-p20: #1aff00;
--cyber-p21: #00ff00; --cyber-p22: #00ff1a; --cyber-p23: #00ff33; --cyber-p24: #00ff4d; --cyber-p25: #00ff66;
--cyber-p26: #00ff80; --cyber-p27: #00ff99; --cyber-p28: #00ffb3; --cyber-p29: #00ffcc; --cyber-p30: #00ffe6;
--cyber-p31: #00ffff; --cyber-p32: #00e6ff; --cyber-p33: #00ccff; --cyber-p34: #00b3ff; --cyber-p35: #0099ff;
--cyber-p36: #0080ff; --cyber-p37: #0066ff; --cyber-p38: #004dff; --cyber-p39: #0033ff; --cyber-p40: #001aff;
--cyber-p41: #0000ff; --cyber-p42: #1a00ff; --cyber-p43: #3300ff; --cyber-p44: #4d00ff; --cyber-p45: #6600ff;
--cyber-p46: #8000ff; --cyber-p47: #9900ff; --cyber-p48: #b300ff; --cyber-p49: #cc00ff; --cyber-p50: #e600ff;
--cyber-p51: #ff00ff; --cyber-p52: #ff00e6; --cyber-p53: #ff00cc; --cyber-p54: #ff00b3; --cyber-p55: #ff0099;
--cyber-p56: #ff0080; --cyber-p57: #ff0066; --cyber-p58: #ff004d; --cyber-p59: #ff0033; --cyber-p60: #ff001a;
--cyber-p61: #ff4080; --cyber-p62: #ff8040; --cyber-p63: #ffbf40; --cyber-p64: #dfff40; --cyber-p65: #9fff40;
--cyber-p66: #60ff40; --cyber-p67: #40ff60; --cyber-p68: #40ff9f; --cyber-p69: #40ffdf; --cyber-p70: #40bfff;
--cyber-p71: #4080ff; --cyber-p72: #4040ff; --cyber-p73: #8040ff; --cyber-p74: #bf40ff; --cyber-p75: #ff40df;
--cyber-p76: #ff409f; --cyber-p77: #ff4060; --cyber-p78: #ff6040; --cyber-p79: #ff9f40; --cyber-p80: #ffdf40;
--cyber-p81: #dfff80; --cyber-p82: #9fff80; --cyber-p83: #60ff80; --cyber-p84: #40ff80; --cyber-p85: #40ff9f;
--cyber-p86: #40ffbf; --cyber-p87: #40ffdf; --cyber-p88: #40dfff; --cyber-p89: #40bfff; --cyber-p90: #409fff;
--cyber-p91: #4080ff; --cyber-p92: #4060ff; --cyber-p93: #6040ff; --cyber-p94: #8040ff; --cyber-p95: #9f40ff;
--cyber-p96: #bf40ff; --cyber-p97: #df40ff; --cyber-p98: #ff40df; --cyber-p99: #ff40bf; --cyber-p100: #ff409f;
/* 核心11个粒子选择 - 动态颜色轮换系统 */
--particle-1: var(--cyber-p5); --particle-2: var(--cyber-p15); --particle-3: var(--cyber-p25); --particle-4: var(--cyber-p35);
--particle-5: var(--cyber-p45); --particle-6: var(--cyber-p55); --particle-7: var(--cyber-p65); --particle-8: var(--cyber-p75);
--particle-9: var(--cyber-p85); --particle-10: var(--cyber-p95); --particle-11: var(--cyber-p50);
/* 动态颜色组 - 用于轮换效果 */
--color-group-1: var(--cyber-p1), var(--cyber-p21), var(--cyber-p41), var(--cyber-p61), var(--cyber-p81);
--color-group-2: var(--cyber-p10), var(--cyber-p30), var(--cyber-p50), var(--cyber-p70), var(--cyber-p90);
--color-group-3: var(--cyber-p5), var(--cyber-p25), var(--cyber-p45), var(--cyber-p65), var(--cyber-p85);
}

/* 注册 CSS 变量以实现平滑动画 */
@property --cyber-border-angle {
syntax: '<angle>';
inherits: false;
initial-value: 0deg;
}

/* 统一所有卡片和按钮的基础样式，为特效做准备 */
.MuiBox-root .css-1ow8u3y, .css-1rgmi2n, .css-bjjbb7, .css-hds0vx,
.css-aafiep, .css-xd8r7u, .css-ya2z3b, .css-8sla8j, .css-ulr2qx, .css-17rlh6j,
.main .bg-primary-foreground, .side .bg-primary-foreground,
.main .bg-foreground, .side .bg-foreground,
.main .bg-content1, .side .bg-content1,
.main .bg-default, .side .bg-default,
.MuiButton-root, .MuiListItemButton-root {
transition: transform 0.3s ease, background-color 0.3s ease !important;
position: relative !important;
overflow: hidden !important; /* 包含内部特效 */
z-index: 1 !important;
}

/* 为特定卡片设置背景和圆角 */
.MuiBox-root .css-1ow8u3y, .css-1rgmi2n, .css-bjjbb7, .css-hds0vx,
.css-aafiep, .css-xd8r7u, .css-ya2z3b, .css-8sla8j, .css-ulr2qx, .css-17rlh6j {
background-color: var(--card-bg-light) !important;
border-radius: var(--border-radius) !important;
}

/* 深色模式下的卡片背景 */
@media (prefers-color-scheme: dark) {
.MuiBox-root .css-1ow8u3y, .css-1rgmi2n, .css-bjjbb7, .css-hds0vx,
.css-aafiep, .css-xd8r7u, .css-ya2z3b, .css-8sla8j, .css-ulr2qx, .css-17rlh6j {
  background-color: var(--card-bg-dark) !important;
}
}

/* 动态边框的容器 - 使用 ::before */
.MuiBox-root .css-1ow8u3y::before, .css-1rgmi2n::before, .css-bjjbb7::before, .css-hds0vx::before,
.css-aafiep::before, .css-xd8r7u::before, .css-ya2z3b::before, .css-8sla8j::before, .css-ulr2qx::before, .css-17rlh6j::before,
.main .bg-primary-foreground::before, .side .bg-primary-foreground::before,
.main .bg-foreground::before, .side .bg-foreground::before,
.main .bg-content1::before, .side .bg-content1::before,
.main .bg-default::before, .side .bg-default::before,
.MuiButton-root::before, .MuiListItemButton-root::before {
content: '' !important;
position: absolute !important;
top: 0; left: 0; right: 0; bottom: 0;
border-radius: inherit !important; /* 继承父元素的圆角 */
padding: 2px !important; /* 边框宽度 */
background: conic-gradient(from var(--cyber-border-angle), var(--cyber-c2), var(--cyber-c1), var(--cyber-c3), var(--cyber-c2)) !important;
-webkit-mask:
  linear-gradient(#fff 0 0) content-box,
  linear-gradient(#fff 0 0) !important;
mask:
  linear-gradient(#fff 0 0) content-box,
  linear-gradient(#fff 0 0) !important;
-webkit-mask-composite: xor !important;
mask-composite: exclude !important;
animation: cyberpunk-border-flow 4s linear infinite !important;
opacity: 0 !important;
transition: opacity 0.4s ease-in-out !important;
z-index: -1 !important;
pointer-events: none !important;
}

/* 全新弧形轨迹粒子效果 - 360度绽放覆盖整个卡片 */
.MuiBox-root .css-1ow8u3y::after, .css-1rgmi2n::after, .css-bjjbb7::after, .css-hds0vx::after,
.css-aafiep::after, .css-xd8r7u::after, .css-ya2z3b::after, .css-8sla8j::after, .css-ulr2qx::after, .css-17rlh6j::after,
.main .bg-primary-foreground::after, .side .bg-primary-foreground::after,
.main .bg-foreground::after, .side .bg-foreground::after,
.main .bg-content1::after, .side .bg-content1::after,
.main .bg-default::after, .side .bg-default::after,
.MuiButton-root::after, .MuiListItemButton-root::after {
content: '' !important;
position: absolute !important;
top: 2px; left: 2px; right: 2px; bottom: 2px; /* 避免覆盖边框 */
border-radius: inherit !important;
overflow: hidden !important;
background-image:
  /* 50+个粒子，统一大小，30+种鲜艳颜色 */
  radial-gradient(var(--particle-red) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-orange) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-yellow) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-lime) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-green) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-cyan) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-blue) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-purple) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-magenta) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-neon-pink) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-neon-green) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-neon-blue) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-neon-orange) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-neon-purple) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-neon-yellow) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-neon-red) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-neon-cyan) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-gold) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-silver) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-copper) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-bronze) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-ruby) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-emerald) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-sapphire) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-diamond) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-amethyst) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-electric) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-fire) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-plasma) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-laser) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-atomic) var(--particle-size), transparent 0),
  /* 额外粒子以达到50+个 */
  radial-gradient(var(--particle-red) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-neon-pink) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-electric) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-gold) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-emerald) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-sapphire) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-fire) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-cyan) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-purple) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-orange) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-lime) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-neon-blue) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-ruby) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-diamond) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-plasma) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-bronze) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-neon-green) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-laser) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-yellow) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-amethyst) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-atomic) var(--particle-size), transparent 0),
  radial-gradient(var(--particle-copper) var(--particle-size), transparent 0) !important;

/* 50+个不同的出生位置 - 覆盖卡片的各个区域 */
background-position:
  5% 8%, 15% 12%, 25% 18%, 35% 22%, 45% 28%, 55% 32%, 65% 38%, 75% 42%, 85% 48%, 95% 52%,
  8% 15%, 18% 25%, 28% 35%, 38% 45%, 48% 55%, 58% 65%, 68% 75%, 78% 85%, 88% 95%, 98% 5%,
  12% 62%, 22% 72%, 32% 82%, 42% 92%, 52% 2%, 62% 12%, 72% 22%, 82% 32%, 92% 42%, 2% 52%,
  7% 77%, 17% 87%, 27% 97%, 37% 7%, 47% 17%, 57% 27%, 67% 37%, 77% 47%, 87% 57%, 97% 67%,
  3% 33%, 13% 43%, 23% 53%, 33% 63%, 43% 73%, 53% 83%, 63% 93%, 73% 3%, 83% 13%, 93% 23%,
  9% 59%, 19% 69%, 29% 79% !important;

background-size:
  /* 50+个不同的背景尺寸，创造更多样化的粒子分布 */
  23px 23px, 31px 31px, 37px 37px, 41px 41px, 43px 43px, 47px 47px, 53px 53px, 59px 59px, 61px 61px, 67px 67px,
  71px 71px, 73px 73px, 79px 79px, 83px 83px, 89px 89px, 97px 97px, 101px 101px, 103px 103px, 107px 107px, 109px 109px,
  113px 113px, 127px 127px, 131px 131px, 137px 137px, 139px 139px, 149px 149px, 151px 151px, 157px 157px, 163px 163px, 167px 167px,
  173px 173px, 179px 179px, 181px 181px, 191px 191px, 193px 193px, 197px 197px, 199px 199px, 211px 211px, 223px 223px, 227px 227px,
  229px 229px, 233px 233px, 239px 239px, 241px 241px, 251px 251px, 257px 257px, 263px 263px, 269px 269px, 271px 271px, 277px 277px,
  281px 281px, 283px 283px, 293px 293px !important;

/* 全新动画系统 - 20+种不同速度，10+种运动轨迹，独立延迟确保粒子独立性 */
animation:
  particle-linear-1 8s linear infinite,
  particle-linear-2 11s linear infinite,
  particle-arc-1 13s ease-in-out infinite,
  particle-arc-2 17s ease-out infinite,
  particle-spiral-1 19s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite,
  particle-spiral-2 23s cubic-bezier(0.55, 0.06, 0.68, 0.19) infinite,
  particle-snake-1 29s ease-in infinite,
  particle-snake-2 31s ease-out infinite,
  particle-rotation-1 37s linear infinite,
  particle-rotation-2 41s ease-in-out infinite,
  particle-random-1 43s cubic-bezier(0.17, 0.67, 0.83, 0.67) infinite,
  particle-random-2 47s cubic-bezier(0.4, 0, 0.6, 1) infinite,
  particle-wave-1 14s ease-in-out infinite,
  particle-wave-2 16s linear infinite,
  particle-pulse-1 18s ease-in-out infinite,
  particle-pulse-2 22s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite,
  particle-orbit-1 26s linear infinite,
  particle-orbit-2 28s ease-in-out infinite,
  particle-zigzag-1 32s ease-out infinite,
  particle-zigzag-2 34s cubic-bezier(0.55, 0.06, 0.68, 0.19) infinite,
  particle-drift-1 38s ease-in infinite,
  particle-drift-2 42s linear infinite,
  particle-bounce-1 45s ease-in-out infinite !important;

/* 独立动画延迟 - 确保每个粒子都有不同的动画周期 */
animation-delay:
  0s, 0.3s, 0.7s, 1.1s, 1.7s, 2.3s, 2.9s, 3.7s, 4.1s, 4.7s,
  5.3s, 5.9s, 6.7s, 7.1s, 7.7s, 8.3s, 8.9s, 9.7s, 10.1s, 10.7s,
  11.3s, 11.9s, 12.7s, 13.1s, 13.7s, 14.3s, 14.9s, 15.7s, 16.1s, 16.7s,
  17.3s, 17.9s, 18.7s, 19.1s, 19.7s, 20.3s, 20.9s, 21.7s, 22.1s, 22.7s,
  23.3s, 23.9s, 24.7s, 25.1s, 25.7s, 26.3s, 26.9s, 27.7s, 28.1s, 28.7s,
  29.3s, 29.9s, 30.7s !important;
opacity: 0 !important;
transition: opacity 0.5s ease-in-out !important;
z-index: 0 !important;
pointer-events: none !important;
}

/* 激活悬停效果 */
.MuiBox-root .css-1ow8u3y:hover, .css-1rgmi2n:hover, .css-bjjbb7:hover, .css-hds0vx:hover,
.css-aafiep:hover, .css-xd8r7u:hover, .css-ya2z3b:hover, .css-8sla8j:hover, .css-ulr2qx:hover, .css-17rlh6j:hover,
.main .bg-primary-foreground:hover, .side .bg-primary-foreground:hover,
.main .bg-foreground:hover, .side .bg-foreground:hover,
.main .bg-content1:hover, .side .bg-content1:hover,
.main .bg-default:hover, .side .bg-default:hover,
.MuiButton-root:hover, .MuiListItemButton-root:hover {
transform: translateY(-4px) !important;
}

/* 悬停时卡片背景透明度变化 */
.MuiBox-root .css-1ow8u3y:hover, .css-1rgmi2n:hover, .css-bjjbb7:hover, .css-hds0vx:hover,
.css-aafiep:hover, .css-xd8r7u:hover, .css-ya2z3b:hover, .css-8sla8j:hover, .css-ulr2qx:hover, .css-17rlh6j:hover {
background-color: var(--card-bg-light-hover) !important;
}
@media (prefers-color-scheme: dark) {
.MuiBox-root .css-1ow8u3y:hover, .css-1rgmi2n:hover, .css-bjjbb7:hover, .css-hds0vx:hover,
.css-aafiep:hover, .css-xd8r7u:hover, .css-ya2z3b:hover, .css-8sla8j:hover, .css-ulr2qx:hover, .css-17rlh6j:hover {
  background-color: var(--card-bg-dark-hover) !important;
}
}

/* 悬停时激活边框并启动透明度脉冲动画 */
.MuiBox-root .css-1ow8u3y:hover::before, .css-1rgmi2n:hover::before, .css-bjjbb7:hover::before, .css-hds0vx:hover::before,
.css-aafiep:hover::before, .css-xd8r7u:hover::before, .css-ya2z3b:hover::before, .css-8sla8j:hover::before, .css-ulr2qx:hover::before, .css-17rlh6j:hover::before,
.main .bg-primary-foreground:hover::before, .side .bg-primary-foreground:hover::before,
.main .bg-foreground:hover::before, .side .bg-foreground:hover::before,
.main .bg-content1:hover::before, .side .bg-content1:hover::before,
.main .bg-default:hover::before, .side .bg-default:hover::before,
.MuiButton-root:hover::before, .MuiListItemButton-root:hover::before {
opacity: 1 !important;
animation: cyberpunk-border-flow 4s linear infinite, cyberpunk-border-opacity-pulse 2s ease-in-out infinite !important;
}

/* 悬停时激活粒子效果 */
.MuiBox-root .css-1ow8u3y:hover::after, .css-1rgmi2n:hover::after, .css-bjjbb7:hover::after, .css-hds0vx:hover::after,
.css-aafiep:hover::after, .css-xd8r7u:hover::after, .css-ya2z3b:hover::after, .css-8sla8j:hover::after, .css-ulr2qx:hover::after, .css-17rlh6j:hover::after,
.main .bg-primary-foreground:hover::after, .side .bg-primary-foreground:hover::after,
.main .bg-foreground:hover::after, .side .bg-foreground:hover::after,
.main .bg-content1:hover::after, .side .bg-content1:hover::after,
.main .bg-default:hover::after, .side .bg-default:hover::after,
.MuiButton-root:hover::after, .MuiListItemButton-root:hover::after {
opacity: 1 !important;
}


/* 导航项目整体盒子*/
.css-dh9epo{
min-width:280px;
}

/* 导航项目样式优化 */
.MuiListItem-root .MuiListItemButton-root {
width: var(--nav-item-width) !important;
border-radius: var(--nav-item-border-radius) !important;
background-color: transparent !important;
margin: 4px 0 !important;
transition: all 0.3s ease !important;
display: flex !important;
align-items: center !important;
justify-content: center !important;
}

/* 导航项目文字水平居中 */
.MuiListItem-root .MuiListItemButton-root .MuiListItemText-root {
display: flex !important;
align-items: center !important;
}

/* ========================================
 Gemini 风格渐变色和悬停效果
 ======================================== */

/* Gemini 风格 CSS 变量定义 */
:root {
--gemini-color-logo-gradient: linear-gradient(90deg,
    #2079fe 0%,
    #098efb 33.53%,
    #ad89eb 70%,
    #ef4e5e 100%);
--gemini-color-white: #ffffff;
--gemini-color-black: #000000;
--gemini-color-grey-50: #f8f9fa;
--gemini-color-grey-100: #f1f3f4;
--gemini-color-grey-200: #e8eaed;
--gemini-color-grey-300: #dadce0;
--gemini-color-grey-400: #bdc1c6;
--gemini-color-grey-500: #9aa0a6;
--gemini-color-grey-600: #80868b;
--gemini-color-grey-700: #5f6368;
--gemini-color-grey-800: #3c4043;
--gemini-color-grey-900: #202124;
--gemini-color-blue-50: #e8f0fe;
--gemini-color-blue-100: #d2e3fc;
--gemini-color-blue-200: #aecbfa;
--gemini-color-blue-300: #8ab4f8;
--gemini-color-blue-400: #669df6;
--gemini-color-blue-500: #4285f4;
--gemini-color-blue-600: #1a73e8;
--gemini-color-blue-700: #1967d2;
--gemini-color-blue-800: #185abc;
--gemini-color-blue-900: #174ea6;
--gemini-color-red-50: #fce8e6;
--gemini-color-red-100: #fad2cf;
--gemini-color-red-200: #f6aea9;
--gemini-color-red-300: #f28b82;
--gemini-color-red-400: #ee675c;
--gemini-color-red-500: #ea4335;
--gemini-color-red-600: #d93025;
--gemini-color-red-700: #c5221f;
--gemini-color-red-800: #b31412;
--gemini-color-red-900: #a50e0e;
--gemini-color-green-50: #e6f4ea;
--gemini-color-green-100: #ceead6;
--gemini-color-green-200: #a8dab5;
--gemini-color-green-300: #81c995;
--gemini-color-green-400: #5bb974;
--gemini-color-green-500: #34a853;
--gemini-color-green-600: #137333;
--gemini-color-green-700: #0d652d;
--gemini-color-green-800: #0b5394;
--gemini-color-green-900: #0a5d00;
--gemini-color-yellow-50: #fef7e0;
--gemini-color-yellow-100: #feefc3;
--gemini-color-yellow-200: #fde047;
--gemini-color-yellow-300: #fcd34d;
--gemini-color-yellow-400: #fbbf24;
--gemini-color-yellow-500: #f59e0b;
--gemini-color-yellow-600: #d97706;
--gemini-color-yellow-700: #b45309;
--gemini-color-yellow-800: #92400e;
--gemini-color-yellow-900: #78350f;
--gemini-color-gemini-blue: #368efe;
--gemini-color-gemini-cyan: #4fabff;
--gemini-color-gemini-light-blue: #b1c5ff;
--gemini-color-blue: #368efe;
--gemini-color-purple-100: #ac87eb;
--gemini-color-red-200: #ee4d5d;
--gemini-color-green-800: #137333;
--gemini-color-blue-800: #185ABC;
--gemini-color-blue-gradient: linear-gradient(61deg, #64b8fb 6.28%, #217bfe 76.97%);
--gemini-color-pink-gradient: linear-gradient(90deg, #a485fa -104.88%, var(--gemini-color-red-200) 198.78%);
--gemini-color-logo-gradient: linear-gradient(90deg, #217bfe 0%, #078efb 33.53%, #ac87eb 70%, #ee4d5d 100%);
--gemini-color-primary-button-gradient: linear-gradient(52deg, #0844ff 11.5%, #64b8fb 129.52%);
--gemini-color-chart-gradient: linear-gradient(105deg, #446eff 18.71%, #2e96ff 49.8%, #b1c5ff 90.55%);
--gemini-color-foreground: var(--gemini-color-white);
--gemini-color-background: var(--gemini-color-grey-900);
--gemini-branding-button-gradient: linear-gradient(15deg, #217BFE 1.02%, #078EFB 28.51%, #A190FF 80.14%, #BD99FE 102.85%);
--gemini-branding-text-gradient: linear-gradient(90deg, #217BFE 0%, #078EFB 33.53%, #AC87EB 67.74%, #EE4D5D 100%);
--gemini-enhanced-text-gradient: linear-gradient(90deg, #1a5fd1 0%, #0670c7 33.53%, #8b6bc2 67.74%, #c73e4e 100%);
--gemini-gradient-linear-colors: var(--gemini-color-gemini-blue) 5.96%, var(--gemini-color-gemini-cyan) 56.89%, var(--gemini-color-gemini-light-blue) 93.53%;
--gemini-gradient-linear: linear-gradient(53deg, #0260FF 9.29%, #40A2FF 48.23%, #A8BEFF 82.56%);
--gemini-text-gradient-light-blue: linear-gradient(69deg, #AABDF4 16.42%, #FFF 77.56%, #A8BEFF 124.91%);

/* 平滑渐变色彩 - 更自然的过渡 */
--smooth-gradient: linear-gradient(90deg,
  #669df6 0%,   /* Blue */
  #7e57c2 25%,  /* Indigo */
  #ab47bc 50%,  /* Purple */
  #ec407a 75%,  /* Pink */
  #ef5350 100%  /* Red */
);

/* 平滑流动渐变 - 更长的过渡距离 */
--smooth-flowing-gradient: linear-gradient(90deg,
  #669df6, #7e57c2, #ab47bc, #ec407a, #ef5350, #ec407a, #ab47bc, #7e57c2, #669df6
);
}


/* 左侧导航文字渐变效果 - 使用平滑渐变 */
.MuiListItemText-primary {
background-image: var(--smooth-gradient) !important;
background-clip: text !important;
-webkit-background-clip: text !important;
-webkit-text-fill-color: transparent !important;
font-weight: bold !important;
background-size: 200% auto !important;
animation: text-gradient-flow 8s linear infinite !important;
left: 50px !important;
width: 100% !important;
}


/* 文字渐变流动动画 */
@keyframes text-gradient-flow {
0% {
  background-position: 0% center !important;
}
100% {
  background-position: 200% center !important;
}
}

/* SVG logo震撼渐变效果 - 多彩流动渐变 */
#layout1 {
position: relative !important;
overflow: hidden !important;
}

/* 为SVG添加动态渐变背景 */
#layout1::before {
content: '' !important;
position: absolute !important;
top: -50% !important;
left: -50% !important;
width: 200% !important;
height: 200% !important;
background: linear-gradient(
  45deg,
  #4285f4 0%,
  #ea4335 15%,
  #fbbc04 30%,
  #34a853 45%,
  #4285f4 60%,
  #ea4335 75%,
  #fbbc04 90%,
  #34a853 100%
) !important;
background-size: 400% 400% !important;
animation: logo-gradient-flow 6s ease-in-out infinite !important;
z-index: -1 !important;
border-radius: 50% !important;
filter: blur(8px) !important;
}

/* SVG路径的震撼效果 */
#layout1 .st1 {
fill: #e22acd !important;

}

/* 震撼的渐变流动动画 */
@keyframes logo-gradient-flow {
0% {
  background-position: 0% 50% !important;
  transform: rotate(0deg) scale(1) !important;
}
25% {
  background-position: 100% 50% !important;
  transform: rotate(90deg) scale(1.1) !important;
}
50% {
  background-position: 100% 100% !important;
  transform: rotate(180deg) scale(1.2) !important;
}
75% {
  background-position: 0% 100% !important;
  transform: rotate(270deg) scale(1.1) !important;
}
100% {
  background-position: 0% 50% !important;
  transform: rotate(360deg) scale(1) !important;
}
}

/* 脉冲效果 */
@keyframes logo-pulse {
0% {
  filter: drop-shadow(0 0 8px rgba(66, 133, 244, 0.6))
          drop-shadow(0 0 16px rgba(234, 67, 53, 0.4))
          drop-shadow(0 0 24px rgba(251, 188, 4, 0.3)) !important;
}
100% {
  filter: drop-shadow(0 0 12px rgba(66, 133, 244, 0.8))
          drop-shadow(0 0 24px rgba(234, 67, 53, 0.6))
          drop-shadow(0 0 36px rgba(251, 188, 4, 0.5)) !important;
}
}

/* 颜色变换动画 */
@keyframes logo-color-shift {
0% { fill: #4285f4 !important; }
25% { fill: #ea4335 !important; }
50% { fill: #fbbc04 !important; }
75% { fill: #34a853 !important; }
100% { fill: #4285f4 !important; }
}

/* data-tauri-drag-region 元素文字渐变效果 */
.css-1l0zim6 {
background-image: var(--smooth-gradient) !important;
background-clip: text !important;
-webkit-background-clip: text !important;
-webkit-text-fill-color: transparent !important;
color: transparent !important;
font-weight: bold !important;
}

/* 右侧设置页面文字渐变效果 - 只针对文字，不影响图标 */
.css-1i24pk4 span:not([class*="MuiSvgIcon"]):not([class*="Icon"]) {
background-image: var(--smooth-gradient) !important;
background-clip: text !important;
-webkit-background-clip: text !important;
-webkit-text-fill-color: transparent !important;
color: transparent !important;
font-weight: bold !important;
font-size: 20px;
}

/* 右侧所有文字渐变效果 - 覆盖更多元素 */
.main span,
.side span,
.main .MuiTypography-root,
.side .MuiTypography-root,
.main .text-sm,
.side .text-sm,
.main .text-base,
.side .text-base,
.main .text-lg,
.side .text-lg,
.main .text-xl,
.side .text-xl,
.main .font-medium,
.side .font-medium,
.main .font-semibold,
.side .font-semibold,
.main .leading-6,
.side .leading-6,
.main .leading-7,
.side .leading-7,
.main .leading-8,
.side .leading-8 {
background-image: var(--smooth-gradient) !important;
background-clip: text !important;
-webkit-background-clip: text !important;
-webkit-text-fill-color: transparent !important;
font-weight: bold !important;
background-size: 200% auto !important;
animation: text-gradient-flow 8s linear infinite !important;
}

/* 赛博朋克边框旋转动画 */
@keyframes cyberpunk-border-flow {
to {
  --cyber-border-angle: 360deg;
}
}

/* 新增：赛博朋克边框透明度脉冲动画 */
@keyframes cyberpunk-border-opacity-pulse {
0%, 100% { opacity: 0.6; }
50% { opacity: 1; }
}

/* 恢复并增强的粒子动画系统 - 随机弧形轨迹，360度任意方向 */

/* 全新粒子颜色系统 - 30+种鲜艳高饱和度颜色 */
:root {
  /* 基础彩虹光谱 - 高饱和度版本 */
  --particle-red: rgba(255, 0, 0, 0.9);
  --particle-orange: rgba(255, 140, 0, 0.9);
  --particle-yellow: rgba(255, 255, 0, 0.9);
  --particle-lime: rgba(50, 205, 50, 0.9);
  --particle-green: rgba(0, 255, 0, 0.9);
  --particle-cyan: rgba(0, 255, 255, 0.9);
  --particle-blue: rgba(0, 100, 255, 0.9);
  --particle-purple: rgba(138, 43, 226, 0.9);
  --particle-magenta: rgba(255, 0, 255, 0.9);

  /* 霓虹色系列 - 超鲜艳 */
  --particle-neon-pink: rgba(255, 20, 147, 1.0);
  --particle-neon-green: rgba(57, 255, 20, 1.0);
  --particle-neon-blue: rgba(0, 191, 255, 1.0);
  --particle-neon-orange: rgba(255, 69, 0, 1.0);
  --particle-neon-purple: rgba(148, 0, 211, 1.0);
  --particle-neon-yellow: rgba(255, 255, 0, 1.0);
  --particle-neon-red: rgba(255, 0, 54, 1.0);
  --particle-neon-cyan: rgba(0, 255, 255, 1.0);

  /* 鲜艳金属色 */
  --particle-gold: rgba(255, 215, 0, 0.95);
  --particle-silver: rgba(220, 220, 220, 0.95);
  --particle-copper: rgba(255, 140, 0, 0.95);
  --particle-bronze: rgba(205, 127, 50, 0.95);

  /* 宝石色系列 - 高亮版 */
  --particle-ruby: rgba(255, 0, 100, 0.95);
  --particle-emerald: rgba(0, 255, 100, 0.95);
  --particle-sapphire: rgba(0, 100, 255, 0.95);
  --particle-diamond: rgba(200, 255, 255, 0.95);
  --particle-amethyst: rgba(200, 100, 255, 0.95);

  /* 特殊鲜艳色 */
  --particle-electric: rgba(0, 255, 255, 1.0);
  --particle-fire: rgba(255, 69, 0, 1.0);
  --particle-plasma: rgba(255, 100, 255, 1.0);
  --particle-laser: rgba(255, 0, 255, 1.0);
  --particle-atomic: rgba(0, 255, 0, 1.0);

  /* 统一粒子大小 */
  --particle-size: 2px;
}

/* 1. 直线运动轨迹 - 多个方向 */
@keyframes particle-linear-1 {
0% { background-position: 5% 8%, 15% 12%, 25% 18%, 35% 22%, 45% 28%, 55% 32%, 65% 38%, 75% 42%, 85% 48%, 95% 52%, 8% 15%, 18% 25%, 28% 35%, 38% 45%, 48% 55%, 58% 65%, 68% 75%, 78% 85%, 88% 95%, 98% 5%, 12% 62%, 22% 72%, 32% 82%, 42% 92%, 52% 2%, 62% 12%, 72% 22%, 82% 32%, 92% 42%, 2% 52%, 7% 77%, 17% 87%, 27% 97%, 37% 7%, 47% 17%, 57% 27%, 67% 37%, 77% 47%, 87% 57%, 97% 67%, 3% 33%, 13% 43%, 23% 53%, 33% 63%, 43% 73%, 53% 83%, 63% 93%, 73% 3%, 83% 13%, 93% 23%, 9% 59%, 19% 69%, 29% 79%; }
100% { background-position: 105% 108%, 115% 112%, 125% 118%, 135% 122%, 145% 128%, 155% 132%, 165% 138%, 175% 142%, 185% 148%, 195% 152%, 108% 115%, 118% 125%, 128% 135%, 138% 145%, 148% 155%, 158% 165%, 168% 175%, 178% 185%, 188% 195%, 198% 105%, 112% 162%, 122% 172%, 132% 182%, 142% 192%, 152% 102%, 162% 112%, 172% 122%, 182% 132%, 192% 142%, 102% 152%, 107% 177%, 117% 187%, 127% 197%, 137% 107%, 147% 117%, 157% 127%, 167% 137%, 177% 147%, 187% 157%, 197% 167%, 103% 133%, 113% 143%, 123% 153%, 133% 163%, 143% 173%, 153% 183%, 163% 193%, 173% 103%, 183% 113%, 193% 123%, 109% 159%, 119% 169%, 129% 179%; }
}

@keyframes particle-linear-2 {
0% { background-position: 5% 8%, 15% 12%, 25% 18%, 35% 22%, 45% 28%, 55% 32%, 65% 38%, 75% 42%, 85% 48%, 95% 52%, 8% 15%, 18% 25%, 28% 35%, 38% 45%, 48% 55%, 58% 65%, 68% 75%, 78% 85%, 88% 95%, 98% 5%, 12% 62%, 22% 72%, 32% 82%, 42% 92%, 52% 2%, 62% 12%, 72% 22%, 82% 32%, 92% 42%, 2% 52%, 7% 77%, 17% 87%, 27% 97%, 37% 7%, 47% 17%, 57% 27%, 67% 37%, 77% 47%, 87% 57%, 97% 67%, 3% 33%, 13% 43%, 23% 53%, 33% 63%, 43% 73%, 53% 83%, 63% 93%, 73% 3%, 83% 13%, 93% 23%, 9% 59%, 19% 69%, 29% 79%; }
100% { background-position: -95% -92%, -85% -88%, -75% -82%, -65% -78%, -55% -72%, -45% -68%, -35% -62%, -25% -58%, -15% -52%, -5% -48%, -92% -85%, -82% -75%, -72% -65%, -62% -55%, -52% -45%, -42% -35%, -32% -25%, -22% -15%, -12% -5%, -2% -95%, -88% -38%, -78% -28%, -68% -18%, -58% -8%, -48% -98%, -38% -88%, -28% -78%, -18% -68%, -8% -58%, -98% -48%, -93% -23%, -83% -13%, -73% -3%, -63% -93%, -53% -83%, -43% -73%, -33% -63%, -23% -53%, -13% -43%, -3% -33%, -97% -67%, -87% -57%, -77% -47%, -67% -37%, -57% -27%, -47% -17%, -37% -7%, -27% -97%, -17% -87%, -7% -77%, -91% -41%, -81% -31%, -71% -21%; }
}

/* 2. 弧形运动轨迹 */
@keyframes particle-arc-1 {
0% { background-position: 5% 8%, 15% 12%, 25% 18%, 35% 22%, 45% 28%, 55% 32%, 65% 38%, 75% 42%, 85% 48%, 95% 52%, 8% 15%, 18% 25%, 28% 35%, 38% 45%, 48% 55%, 58% 65%, 68% 75%, 78% 85%, 88% 95%, 98% 5%, 12% 62%, 22% 72%, 32% 82%, 42% 92%, 52% 2%, 62% 12%, 72% 22%, 82% 32%, 92% 42%, 2% 52%, 7% 77%, 17% 87%, 27% 97%, 37% 7%, 47% 17%, 57% 27%, 67% 37%, 77% 47%, 87% 57%, 97% 67%, 3% 33%, 13% 43%, 23% 53%, 33% 63%, 43% 73%, 53% 83%, 63% 93%, 73% 3%, 83% 13%, 93% 23%, 9% 59%, 19% 69%, 29% 79%; }
25% { background-position: 25% 28%, 35% 32%, 45% 38%, 55% 42%, 65% 48%, 75% 52%, 85% 58%, 95% 62%, 105% 68%, 115% 72%, 28% 35%, 38% 45%, 48% 55%, 58% 65%, 68% 75%, 78% 85%, 88% 95%, 98% 105%, 108% 115%, 118% 25%, 32% 82%, 42% 92%, 52% 102%, 62% 112%, 72% 22%, 82% 32%, 92% 42%, 102% 52%, 112% 62%, 22% 72%, 27% 97%, 37% 107%, 47% 117%, 57% 27%, 67% 37%, 77% 47%, 87% 57%, 97% 67%, 107% 77%, 117% 87%, 23% 53%, 33% 63%, 43% 73%, 53% 83%, 63% 93%, 73% 103%, 83% 113%, 93% 23%, 103% 33%, 113% 43%, 29% 79%, 39% 89%, 49% 99%; }
50% { background-position: 45% 48%, 55% 52%, 65% 58%, 75% 62%, 85% 68%, 95% 72%, 105% 78%, 115% 82%, 125% 88%, 135% 92%, 48% 55%, 58% 65%, 68% 75%, 78% 85%, 88% 95%, 98% 105%, 108% 115%, 118% 125%, 128% 135%, 138% 45%, 52% 102%, 62% 112%, 72% 122%, 82% 132%, 92% 42%, 102% 52%, 112% 62%, 122% 72%, 132% 82%, 42% 92%, 47% 117%, 57% 127%, 67% 137%, 77% 47%, 87% 57%, 97% 67%, 107% 77%, 117% 87%, 127% 97%, 137% 107%, 43% 73%, 53% 83%, 63% 93%, 73% 103%, 83% 113%, 93% 123%, 103% 133%, 113% 43%, 123% 53%, 133% 63%, 49% 99%, 59% 109%, 69% 119%; }
75% { background-position: 65% 68%, 75% 72%, 85% 78%, 95% 82%, 105% 88%, 115% 92%, 125% 98%, 135% 102%, 145% 108%, 155% 112%, 68% 75%, 78% 85%, 88% 95%, 98% 105%, 108% 115%, 118% 125%, 128% 135%, 138% 145%, 148% 155%, 158% 65%, 72% 122%, 82% 132%, 92% 142%, 102% 152%, 112% 62%, 122% 72%, 132% 82%, 142% 92%, 152% 102%, 62% 112%, 67% 137%, 77% 147%, 87% 157%, 97% 67%, 107% 77%, 117% 87%, 127% 97%, 137% 107%, 147% 117%, 157% 127%, 63% 93%, 73% 103%, 83% 113%, 93% 123%, 103% 133%, 113% 143%, 123% 153%, 133% 63%, 143% 73%, 153% 83%, 69% 119%, 79% 129%, 89% 139%; }
100% { background-position: 85% 88%, 95% 92%, 105% 98%, 115% 102%, 125% 108%, 135% 112%, 145% 118%, 155% 122%, 165% 128%, 175% 132%, 88% 95%, 98% 105%, 108% 115%, 118% 125%, 128% 135%, 138% 145%, 148% 155%, 158% 165%, 168% 175%, 178% 85%, 92% 142%, 102% 152%, 112% 162%, 122% 172%, 132% 82%, 142% 92%, 152% 102%, 162% 112%, 172% 122%, 82% 132%, 87% 157%, 97% 167%, 107% 177%, 117% 87%, 127% 97%, 137% 107%, 147% 117%, 157% 127%, 167% 137%, 177% 147%, 83% 113%, 93% 123%, 103% 133%, 113% 143%, 123% 153%, 133% 163%, 143% 173%, 153% 83%, 163% 93%, 173% 103%, 89% 139%, 99% 149%, 109% 159%; }
}

@keyframes particle-arc-2 {
0% { background-position: 5% 8%, 15% 12%, 25% 18%, 35% 22%, 45% 28%, 55% 32%, 65% 38%, 75% 42%, 85% 48%, 95% 52%, 8% 15%, 18% 25%, 28% 35%, 38% 45%, 48% 55%, 58% 65%, 68% 75%, 78% 85%, 88% 95%, 98% 5%, 12% 62%, 22% 72%, 32% 82%, 42% 92%, 52% 2%, 62% 12%, 72% 22%, 82% 32%, 92% 42%, 2% 52%, 7% 77%, 17% 87%, 27% 97%, 37% 7%, 47% 17%, 57% 27%, 67% 37%, 77% 47%, 87% 57%, 97% 67%, 3% 33%, 13% 43%, 23% 53%, 33% 63%, 43% 73%, 53% 83%, 63% 93%, 73% 3%, 83% 13%, 93% 23%, 9% 59%, 19% 69%, 29% 79%; }
25% { background-position: 15% 18%, 25% 22%, 35% 28%, 45% 32%, 55% 38%, 65% 42%, 75% 48%, 85% 52%, 95% 58%, 105% 62%, 18% 25%, 28% 35%, 38% 45%, 48% 55%, 58% 65%, 68% 75%, 78% 85%, 88% 95%, 98% 105%, 108% 15%, 22% 72%, 32% 82%, 42% 92%, 52% 102%, 62% 12%, 72% 22%, 82% 32%, 92% 42%, 102% 52%, 12% 62%, 17% 87%, 27% 97%, 37% 107%, 47% 17%, 57% 27%, 67% 37%, 77% 47%, 87% 57%, 97% 67%, 107% 77%, 13% 43%, 23% 53%, 33% 63%, 43% 73%, 53% 83%, 63% 93%, 73% 103%, 83% 13%, 93% 23%, 103% 33%, 19% 69%, 29% 79%, 39% 89%; }
50% { background-position: 35% 38%, 45% 42%, 55% 48%, 65% 52%, 75% 58%, 85% 62%, 95% 68%, 105% 72%, 115% 78%, 125% 82%, 38% 45%, 48% 55%, 58% 65%, 68% 75%, 78% 85%, 88% 95%, 98% 105%, 108% 115%, 118% 125%, 128% 35%, 42% 92%, 52% 102%, 62% 112%, 72% 122%, 82% 32%, 92% 42%, 102% 52%, 112% 62%, 122% 72%, 32% 82%, 37% 107%, 47% 117%, 57% 127%, 67% 37%, 77% 47%, 87% 57%, 97% 67%, 107% 77%, 117% 87%, 127% 97%, 33% 63%, 43% 73%, 53% 83%, 63% 93%, 73% 103%, 83% 113%, 93% 123%, 103% 33%, 113% 43%, 123% 53%, 39% 89%, 49% 99%, 59% 109%; }
75% { background-position: 55% 58%, 65% 62%, 75% 68%, 85% 72%, 95% 78%, 105% 82%, 115% 88%, 125% 92%, 135% 98%, 145% 102%, 58% 65%, 68% 75%, 78% 85%, 88% 95%, 98% 105%, 108% 115%, 118% 125%, 128% 135%, 138% 145%, 148% 55%, 62% 112%, 72% 122%, 82% 132%, 92% 142%, 102% 52%, 112% 62%, 122% 72%, 132% 82%, 142% 92%, 52% 102%, 57% 127%, 67% 137%, 77% 147%, 87% 57%, 97% 67%, 107% 77%, 117% 87%, 127% 97%, 137% 107%, 147% 117%, 53% 83%, 63% 93%, 73% 103%, 83% 113%, 93% 123%, 103% 133%, 113% 143%, 123% 53%, 133% 63%, 143% 73%, 59% 109%, 69% 119%, 79% 129%; }
100% { background-position: 75% 78%, 85% 82%, 95% 88%, 105% 92%, 115% 98%, 125% 102%, 135% 108%, 145% 112%, 155% 118%, 165% 122%, 78% 85%, 88% 95%, 98% 105%, 108% 115%, 118% 125%, 128% 135%, 138% 145%, 148% 155%, 158% 165%, 168% 75%, 82% 132%, 92% 142%, 102% 152%, 112% 162%, 122% 72%, 132% 82%, 142% 92%, 152% 102%, 162% 112%, 72% 122%, 77% 147%, 87% 157%, 97% 167%, 107% 77%, 117% 87%, 127% 97%, 137% 107%, 147% 117%, 157% 127%, 167% 137%, 73% 103%, 83% 113%, 93% 123%, 103% 133%, 113% 143%, 123% 153%, 133% 163%, 143% 73%, 153% 83%, 163% 93%, 79% 129%, 89% 139%, 99% 149%; }
}

/* 3. 螺旋运动轨迹 */
@keyframes particle-spiral-1 {
0% { background-position: 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%; }
25% { background-position: 60% 45%, 55% 40%, 65% 55%, 45% 60%, 70% 50%, 50% 70%, 40% 45%, 55% 30%, 75% 55%, 35% 65%, 80% 50%, 50% 80%, 30% 40%, 60% 25%, 85% 60%, 25% 70%, 90% 50%, 50% 90%, 20% 35%, 65% 20%, 95% 65%, 15% 75%, 100% 50%, 50% 100%, 10% 30%, 70% 15%, 105% 70%, 5% 80%, 110% 50%, 50% 110%, 0% 25%, 75% 10%, 115% 75%, -5% 85%, 120% 50%, 50% 120%, -10% 20%, 80% 5%, 125% 80%, -15% 90%, 130% 50%, 50% 130%, -20% 15%, 85% 0%, 135% 85%, -25% 95%, 140% 50%, 50% 140%, -30% 10%; }
50% { background-position: 70% 40%, 60% 30%, 80% 60%, 40% 70%, 90% 50%, 50% 90%, 30% 40%, 60% 20%, 100% 60%, 20% 80%, 110% 50%, 50% 110%, 10% 30%, 70% 10%, 120% 70%, 0% 90%, 130% 50%, 50% 130%, -10% 20%, 80% 0%, 140% 80%, -20% 100%, 150% 50%, 50% 150%, -30% 10%, 90% -10%, 160% 90%, -40% 110%, 170% 50%, 50% 170%, -50% 0%, 100% -20%, 180% 100%, -60% 120%, 190% 50%, 50% 190%, -70% -10%, 110% -30%, 200% 110%, -80% 130%, 210% 50%, 50% 210%, -90% -20%, 120% -40%, 220% 120%, -100% 140%, 230% 50%, 50% 230%, -110% -30%; }
75% { background-position: 80% 35%, 65% 20%, 95% 65%, 35% 80%, 110% 50%, 50% 110%, 20% 35%, 65% 10%, 125% 65%, 5% 95%, 140% 50%, 50% 140%, -10% 20%, 80% 0%, 155% 80%, -25% 110%, 170% 50%, 50% 170%, -40% 5%, 95% -15%, 185% 95%, -55% 125%, 200% 50%, 50% 200%, -70% -10%, 110% -30%, 215% 110%, -85% 140%, 230% 50%, 50% 230%, -100% -25%, 125% -45%, 245% 125%, -115% 155%, 260% 50%, 50% 260%, -130% -40%, 140% -60%, 275% 140%, -145% 170%, 290% 50%, 50% 290%, -160% -55%, 155% -75%, 305% 155%, -175% 185%, 320% 50%, 50% 320%, -190% -70%; }
100% { background-position: 90% 30%, 70% 10%, 110% 70%, 30% 90%, 130% 50%, 50% 130%, 10% 30%, 70% 0%, 150% 70%, -10% 110%, 170% 50%, 50% 170%, -30% 10%, 90% -10%, 190% 90%, -50% 130%, 210% 50%, 50% 210%, -70% -10%, 110% -30%, 230% 110%, -90% 150%, 250% 50%, 50% 250%, -110% -30%, 130% -50%, 270% 130%, -130% 170%, 290% 50%, 50% 290%, -150% -50%, 150% -70%, 310% 150%, -170% 190%, 330% 50%, 50% 330%, -190% -70%, 170% -90%, 350% 170%, -210% 210%, 370% 50%, 50% 370%, -230% -90%, 190% -110%, 390% 190%, -250% 230%, 410% 50%, 50% 410%, -270% -110%; }
}

@keyframes particle-spiral-2 {
0% { background-position: 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%; }
25% { background-position: 45% 60%, 40% 55%, 55% 65%, 60% 45%, 50% 70%, 70% 50%, 45% 40%, 30% 55%, 55% 75%, 65% 35%, 50% 80%, 80% 50%, 40% 30%, 25% 60%, 60% 85%, 70% 25%, 50% 90%, 90% 50%, 35% 20%, 20% 65%, 65% 95%, 75% 15%, 50% 100%, 100% 50%, 30% 10%, 15% 70%, 70% 105%, 80% 5%, 50% 110%, 110% 50%, 25% 0%, 10% 75%, 75% 115%, 85% -5%, 50% 120%, 120% 50%, 20% -10%, 5% 80%, 80% 125%, 90% -15%, 50% 130%, 130% 50%, 15% -20%, 0% 85%, 85% 135%, 95% -25%, 50% 140%, 140% 50%, 10% -30%; }
50% { background-position: 40% 70%, 30% 60%, 60% 80%, 70% 40%, 50% 90%, 90% 50%, 40% 30%, 20% 60%, 60% 100%, 80% 20%, 50% 110%, 110% 50%, 30% 10%, 10% 70%, 70% 120%, 90% 0%, 50% 130%, 130% 50%, 20% -10%, 0% 80%, 80% 140%, 100% -20%, 50% 150%, 150% 50%, 10% -30%, -10% 90%, 90% 160%, 110% -40%, 50% 170%, 170% 50%, 0% -50%, -20% 100%, 100% 180%, 120% -60%, 50% 190%, 190% 50%, -10% -70%, -30% 110%, 110% 200%, 130% -80%, 50% 210%, 210% 50%, -20% -90%, -40% 120%, 120% 220%, 140% -100%, 50% 230%, 230% 50%, -30% -110%; }
75% { background-position: 35% 80%, 20% 65%, 65% 95%, 80% 35%, 50% 110%, 110% 50%, 35% 20%, 10% 65%, 65% 125%, 95% 5%, 50% 140%, 140% 50%, 20% -10%, 0% 80%, 80% 155%, 110% -25%, 50% 170%, 170% 50%, 5% -40%, -15% 95%, 95% 185%, 125% -55%, 50% 200%, 200% 50%, -10% -70%, -30% 110%, 110% 215%, 140% -85%, 50% 230%, 230% 50%, -25% -100%, -45% 125%, 125% 245%, 155% -115%, 50% 260%, 260% 50%, -40% -130%, -60% 140%, 140% 275%, 170% -145%, 50% 290%, 290% 50%, -55% -160%, -75% 155%, 155% 305%, 185% -175%, 50% 320%, 320% 50%, -70% -190%; }
100% { background-position: 30% 90%, 10% 70%, 70% 110%, 90% 30%, 50% 130%, 130% 50%, 30% 10%, 0% 70%, 70% 150%, 110% -10%, 50% 170%, 170% 50%, 10% -30%, -10% 90%, 90% 190%, 130% -50%, 50% 210%, 210% 50%, -10% -70%, -30% 110%, 110% 230%, 150% -90%, 50% 250%, 250% 50%, -30% -110%, -50% 130%, 130% 270%, 170% -130%, 50% 290%, 290% 50%, -50% -150%, -70% 150%, 150% 310%, 190% -170%, 50% 330%, 330% 50%, -70% -190%, -90% 170%, 170% 350%, 210% -210%, 50% 370%, 370% 50%, -90% -230%, -110% 190%, 190% 390%, 230% -250%, 50% 410%, 410% 50%, -110% -270%; }
}

/* 4. 蛇形运动轨迹 */
@keyframes particle-snake-1 {
0% { background-position: 5% 50%, 15% 50%, 25% 50%, 35% 50%, 45% 50%, 55% 50%, 65% 50%, 75% 50%, 85% 50%, 95% 50%, 8% 50%, 18% 50%, 28% 50%, 38% 50%, 48% 50%, 58% 50%, 68% 50%, 78% 50%, 88% 50%, 98% 50%, 12% 50%, 22% 50%, 32% 50%, 42% 50%, 52% 50%, 62% 50%, 72% 50%, 82% 50%, 92% 50%, 2% 50%, 7% 50%, 17% 50%, 27% 50%, 37% 50%, 47% 50%, 57% 50%, 67% 50%, 77% 50%, 87% 50%, 97% 50%, 3% 50%, 13% 50%, 23% 50%, 33% 50%, 43% 50%, 53% 50%, 63% 50%, 73% 50%, 83% 50%, 93% 50%, 9% 50%, 19% 50%, 29% 50%; }
25% { background-position: 25% 30%, 35% 70%, 45% 30%, 55% 70%, 65% 30%, 75% 70%, 85% 30%, 95% 70%, 105% 30%, 115% 70%, 28% 30%, 38% 70%, 48% 30%, 58% 70%, 68% 30%, 78% 70%, 88% 30%, 98% 70%, 108% 30%, 118% 70%, 32% 30%, 42% 70%, 52% 30%, 62% 70%, 72% 30%, 82% 70%, 92% 30%, 102% 70%, 112% 30%, 22% 70%, 27% 30%, 37% 70%, 47% 30%, 57% 70%, 67% 30%, 77% 70%, 87% 30%, 97% 70%, 107% 30%, 117% 70%, 23% 30%, 33% 70%, 43% 30%, 53% 70%, 63% 30%, 73% 70%, 83% 30%, 93% 70%, 103% 30%, 113% 70%, 29% 30%, 39% 70%, 49% 30%; }
50% { background-position: 45% 20%, 55% 80%, 65% 20%, 75% 80%, 85% 20%, 95% 80%, 105% 20%, 115% 80%, 125% 20%, 135% 80%, 48% 20%, 58% 80%, 68% 20%, 78% 80%, 88% 20%, 98% 80%, 108% 20%, 118% 80%, 128% 20%, 138% 80%, 52% 20%, 62% 80%, 72% 20%, 82% 80%, 92% 20%, 102% 80%, 112% 20%, 122% 80%, 132% 20%, 42% 80%, 47% 20%, 57% 80%, 67% 20%, 77% 80%, 87% 20%, 97% 80%, 107% 20%, 117% 80%, 127% 20%, 137% 80%, 43% 20%, 53% 80%, 63% 20%, 73% 80%, 83% 20%, 93% 80%, 103% 20%, 113% 80%, 123% 20%, 133% 80%, 49% 20%, 59% 80%, 69% 20%; }
75% { background-position: 65% 10%, 75% 90%, 85% 10%, 95% 90%, 105% 10%, 115% 90%, 125% 10%, 135% 90%, 145% 10%, 155% 90%, 68% 10%, 78% 90%, 88% 10%, 98% 90%, 108% 10%, 118% 90%, 128% 10%, 138% 90%, 148% 10%, 158% 90%, 72% 10%, 82% 90%, 92% 10%, 102% 90%, 112% 10%, 122% 90%, 132% 10%, 142% 90%, 152% 10%, 62% 90%, 67% 10%, 77% 90%, 87% 10%, 97% 90%, 107% 10%, 117% 90%, 127% 10%, 137% 90%, 147% 10%, 157% 90%, 63% 10%, 73% 90%, 83% 10%, 93% 90%, 103% 10%, 113% 90%, 123% 10%, 133% 90%, 143% 10%, 153% 90%, 69% 10%, 79% 90%, 89% 10%; }
100% { background-position: 85% 0%, 95% 100%, 105% 0%, 115% 100%, 125% 0%, 135% 100%, 145% 0%, 155% 100%, 165% 0%, 175% 100%, 88% 0%, 98% 100%, 108% 0%, 118% 100%, 128% 0%, 138% 100%, 148% 0%, 158% 100%, 168% 0%, 178% 100%, 92% 0%, 102% 100%, 112% 0%, 122% 100%, 132% 0%, 142% 100%, 152% 0%, 162% 100%, 172% 0%, 82% 100%, 87% 0%, 97% 100%, 107% 0%, 117% 100%, 127% 0%, 137% 100%, 147% 0%, 157% 100%, 167% 0%, 177% 100%, 83% 0%, 93% 100%, 103% 0%, 113% 100%, 123% 0%, 133% 100%, 143% 0%, 153% 100%, 163% 0%, 173% 100%, 89% 0%, 99% 100%, 109% 0%; }
}

@keyframes particle-snake-2 {
0% { background-position: 5% 50%, 15% 50%, 25% 50%, 35% 50%, 45% 50%, 55% 50%, 65% 50%, 75% 50%, 85% 50%, 95% 50%, 8% 50%, 18% 50%, 28% 50%, 38% 50%, 48% 50%, 58% 50%, 68% 50%, 78% 50%, 88% 50%, 98% 50%, 12% 50%, 22% 50%, 32% 50%, 42% 50%, 52% 50%, 62% 50%, 72% 50%, 82% 50%, 92% 50%, 2% 50%, 7% 50%, 17% 50%, 27% 50%, 37% 50%, 47% 50%, 57% 50%, 67% 50%, 77% 50%, 87% 50%, 97% 50%, 3% 50%, 13% 50%, 23% 50%, 33% 50%, 43% 50%, 53% 50%, 63% 50%, 73% 50%, 83% 50%, 93% 50%, 9% 50%, 19% 50%, 29% 50%; }
25% { background-position: 25% 70%, 35% 30%, 45% 70%, 55% 30%, 65% 70%, 75% 30%, 85% 70%, 95% 30%, 105% 70%, 115% 30%, 28% 70%, 38% 30%, 48% 70%, 58% 30%, 68% 70%, 78% 30%, 88% 70%, 98% 30%, 108% 70%, 118% 30%, 32% 70%, 42% 30%, 52% 70%, 62% 30%, 72% 70%, 82% 30%, 92% 70%, 102% 30%, 112% 70%, 22% 30%, 27% 70%, 37% 30%, 47% 70%, 57% 30%, 67% 70%, 77% 30%, 87% 70%, 97% 30%, 107% 70%, 117% 30%, 23% 70%, 33% 30%, 43% 70%, 53% 30%, 63% 70%, 73% 30%, 83% 70%, 93% 30%, 103% 70%, 113% 30%, 29% 70%, 39% 30%, 49% 70%; }
50% { background-position: 45% 80%, 55% 20%, 65% 80%, 75% 20%, 85% 80%, 95% 20%, 105% 80%, 115% 20%, 125% 80%, 135% 20%, 48% 80%, 58% 20%, 68% 80%, 78% 20%, 88% 80%, 98% 20%, 108% 80%, 118% 20%, 128% 80%, 138% 20%, 52% 80%, 62% 20%, 72% 80%, 82% 20%, 92% 80%, 102% 20%, 112% 80%, 122% 20%, 132% 80%, 42% 20%, 47% 80%, 57% 20%, 67% 80%, 77% 20%, 87% 80%, 97% 20%, 107% 80%, 117% 20%, 127% 80%, 137% 20%, 43% 80%, 53% 20%, 63% 80%, 73% 20%, 83% 80%, 93% 20%, 103% 80%, 113% 20%, 123% 80%, 133% 20%, 49% 80%, 59% 20%, 69% 80%; }
75% { background-position: 65% 90%, 75% 10%, 85% 90%, 95% 10%, 105% 90%, 115% 10%, 125% 90%, 135% 10%, 145% 90%, 155% 10%, 68% 90%, 78% 10%, 88% 90%, 98% 10%, 108% 90%, 118% 10%, 128% 90%, 138% 10%, 148% 90%, 158% 10%, 72% 90%, 82% 10%, 92% 90%, 102% 10%, 112% 90%, 122% 10%, 132% 90%, 142% 10%, 152% 90%, 62% 10%, 67% 90%, 77% 10%, 87% 90%, 97% 10%, 107% 90%, 117% 10%, 127% 90%, 137% 10%, 147% 90%, 157% 10%, 63% 90%, 73% 10%, 83% 90%, 93% 10%, 103% 90%, 113% 10%, 123% 90%, 133% 10%, 143% 90%, 153% 10%, 69% 90%, 79% 10%, 89% 90%; }
100% { background-position: 85% 100%, 95% 0%, 105% 100%, 115% 0%, 125% 100%, 135% 0%, 145% 100%, 155% 0%, 165% 100%, 175% 0%, 88% 100%, 98% 0%, 108% 100%, 118% 0%, 128% 100%, 138% 0%, 148% 100%, 158% 0%, 168% 100%, 178% 0%, 92% 100%, 102% 0%, 112% 100%, 122% 0%, 132% 100%, 142% 0%, 152% 100%, 162% 0%, 172% 100%, 82% 0%, 87% 100%, 97% 0%, 107% 100%, 117% 0%, 127% 100%, 137% 0%, 147% 100%, 157% 0%, 167% 100%, 177% 0%, 83% 100%, 93% 0%, 103% 100%, 113% 0%, 123% 100%, 133% 0%, 143% 100%, 153% 0%, 163% 100%, 173% 0%, 89% 100%, 99% 0%, 109% 100%; }
}

/* 5. 旋转运动轨迹 */
@keyframes particle-rotation-1 {
0% { background-position: 50% 30%, 70% 50%, 50% 70%, 30% 50%, 60% 40%, 60% 60%, 40% 60%, 40% 40%, 55% 35%, 65% 55%, 45% 65%, 35% 45%, 75% 50%, 50% 75%, 25% 50%, 50% 25%, 80% 50%, 50% 80%, 20% 50%, 50% 20%, 85% 50%, 50% 85%, 15% 50%, 50% 15%, 90% 50%, 50% 90%, 10% 50%, 50% 10%, 95% 50%, 50% 95%, 5% 50%, 50% 5%, 100% 50%, 50% 100%, 0% 50%, 50% 0%, 105% 50%, 50% 105%, -5% 50%, 50% -5%, 110% 50%, 50% 110%, -10% 50%, 50% -10%, 115% 50%, 50% 115%, -15% 50%, 50% -15%; }
25% { background-position: 70% 30%, 70% 70%, 30% 70%, 30% 30%, 80% 40%, 60% 80%, 20% 60%, 40% 20%, 75% 35%, 65% 75%, 25% 65%, 35% 25%, 95% 50%, 50% 95%, 5% 50%, 50% 5%, 100% 50%, 50% 100%, 0% 50%, 50% 0%, 105% 50%, 50% 105%, -5% 50%, 50% -5%, 110% 50%, 50% 110%, -10% 50%, 50% -10%, 115% 50%, 50% 115%, -15% 50%, 50% -15%, 120% 50%, 50% 120%, -20% 50%, 50% -20%, 125% 50%, 50% 125%, -25% 50%, 50% -25%, 130% 50%, 50% 130%, -30% 50%, 50% -30%, 135% 50%, 50% 135%, -35% 50%, 50% -35%; }
50% { background-position: 70% 50%, 50% 70%, 30% 50%, 50% 30%, 80% 60%, 40% 80%, 20% 40%, 60% 20%, 75% 55%, 45% 75%, 25% 45%, 55% 25%, 95% 75%, 25% 95%, 5% 25%, 75% 5%, 100% 80%, 20% 100%, 0% 20%, 80% 0%, 105% 85%, 15% 105%, -5% 15%, 85% -5%, 110% 90%, 10% 110%, -10% 10%, 90% -10%, 115% 95%, 5% 115%, -15% 5%, 95% -15%, 120% 100%, 0% 120%, -20% 0%, 100% -20%, 125% 105%, -5% 125%, -25% -5%, 105% -25%, 130% 110%, -10% 130%, -30% -10%, 110% -30%, 135% 115%, -15% 135%, -35% -15%, 115% -35%; }
75% { background-position: 50% 70%, 30% 50%, 50% 30%, 70% 50%, 60% 80%, 20% 60%, 40% 20%, 80% 40%, 55% 75%, 25% 55%, 45% 25%, 75% 45%, 75% 95%, 5% 75%, 25% 5%, 95% 25%, 80% 100%, 0% 80%, 20% 0%, 100% 20%, 85% 105%, -5% 85%, 15% -5%, 105% 15%, 90% 110%, -10% 90%, 10% -10%, 110% 10%, 95% 115%, -15% 95%, 5% -15%, 115% 5%, 100% 120%, -20% 100%, 0% -20%, 120% 0%, 105% 125%, -25% 105%, -5% -25%, 125% -5%, 110% 130%, -30% 110%, -10% -30%, 130% -10%, 115% 135%, -35% 115%, -15% -35%, 135% -15%; }
100% { background-position: 30% 50%, 50% 30%, 70% 50%, 50% 70%, 40% 60%, 60% 40%, 40% 60%, 60% 40%, 35% 55%, 55% 35%, 45% 65%, 65% 45%, 50% 75%, 75% 50%, 25% 75%, 25% 50%, 50% 80%, 80% 50%, 20% 80%, 20% 50%, 50% 85%, 85% 50%, 15% 85%, 15% 50%, 50% 90%, 90% 50%, 10% 90%, 10% 50%, 50% 95%, 95% 50%, 5% 95%, 5% 50%, 50% 100%, 100% 50%, 0% 100%, 0% 50%, 50% 105%, 105% 50%, -5% 105%, -5% 50%, 50% 110%, 110% 50%, -10% 110%, -10% 50%, 50% 115%, 115% 50%, -15% 115%, -15% 50%; }
}

@keyframes particle-rotation-2 {
0% { background-position: 50% 20%, 80% 50%, 50% 80%, 20% 50%, 70% 30%, 70% 70%, 30% 70%, 30% 30%, 65% 25%, 75% 65%, 25% 75%, 35% 25%, 85% 50%, 50% 85%, 15% 50%, 50% 15%, 90% 50%, 50% 90%, 10% 50%, 50% 10%, 95% 50%, 50% 95%, 5% 50%, 50% 5%, 100% 50%, 50% 100%, 0% 50%, 50% 0%, 105% 50%, 50% 105%, -5% 50%, 50% -5%, 110% 50%, 50% 110%, -10% 50%, 50% -10%, 115% 50%, 50% 115%, -15% 50%, 50% -15%, 120% 50%, 50% 120%, -20% 50%, 50% -20%, 125% 50%, 50% 125%, -25% 50%, 50% -25%; }
25% { background-position: 80% 20%, 80% 80%, 20% 80%, 20% 20%, 90% 30%, 70% 90%, 10% 70%, 30% 10%, 85% 25%, 75% 85%, 15% 75%, 25% 15%, 105% 50%, 50% 105%, -5% 50%, 50% -5%, 110% 50%, 50% 110%, -10% 50%, 50% -10%, 115% 50%, 50% 115%, -15% 50%, 50% -15%, 120% 50%, 50% 120%, -20% 50%, 50% -20%, 125% 50%, 50% 125%, -25% 50%, 50% -25%, 130% 50%, 50% 130%, -30% 50%, 50% -30%, 135% 50%, 50% 135%, -35% 50%, 50% -35%, 140% 50%, 50% 140%, -40% 50%, 50% -40%, 145% 50%, 50% 145%, -45% 50%, 50% -45%; }
50% { background-position: 80% 50%, 50% 80%, 20% 50%, 50% 20%, 90% 70%, 30% 90%, 10% 30%, 70% 10%, 85% 65%, 35% 85%, 15% 35%, 65% 15%, 105% 85%, 15% 105%, -5% 15%, 85% -5%, 110% 90%, 10% 110%, -10% 10%, 90% -10%, 115% 95%, 5% 115%, -15% 5%, 95% -15%, 120% 100%, 0% 120%, -20% 0%, 100% -20%, 125% 105%, -5% 125%, -25% -5%, 105% -25%, 130% 110%, -10% 130%, -30% -10%, 110% -30%, 135% 115%, -15% 135%, -35% -15%, 115% -35%, 140% 120%, -20% 140%, -40% -20%, 120% -40%, 145% 125%, -25% 145%, -45% -25%, 125% -45%; }
75% { background-position: 50% 80%, 20% 50%, 50% 20%, 80% 50%, 70% 90%, 10% 70%, 30% 10%, 90% 30%, 65% 85%, 15% 65%, 35% 15%, 85% 35%, 85% 105%, -5% 85%, 15% -5%, 105% 15%, 90% 110%, -10% 90%, 10% -10%, 110% 10%, 95% 115%, -15% 95%, 5% -15%, 115% 5%, 100% 120%, -20% 100%, 0% -20%, 120% 0%, 105% 125%, -25% 105%, -5% -25%, 125% -5%, 110% 130%, -30% 110%, -10% -30%, 130% -10%, 115% 135%, -35% 115%, -15% -35%, 135% -15%, 120% 140%, -40% 120%, -20% -40%, 140% -20%, 125% 145%, -45% 125%, -25% -45%, 145% -25%; }
100% { background-position: 20% 50%, 50% 20%, 80% 50%, 50% 80%, 30% 70%, 70% 30%, 30% 70%, 70% 30%, 25% 65%, 65% 25%, 35% 75%, 75% 35%, 50% 85%, 85% 50%, 15% 85%, 15% 50%, 50% 90%, 90% 50%, 10% 90%, 10% 50%, 50% 95%, 95% 50%, 5% 95%, 5% 50%, 50% 100%, 100% 50%, 0% 100%, 0% 50%, 50% 105%, 105% 50%, -5% 105%, -5% 50%, 50% 110%, 110% 50%, -10% 110%, -10% 50%, 50% 115%, 115% 50%, -15% 115%, -15% 50%, 50% 120%, 120% 50%, -20% 120%, -20% 50%, 50% 125%, 125% 50%, -25% 125%, -25% 50%; }
}

/* 6. 随机运动轨迹 */
@keyframes particle-random-1 {
0% { background-position: 5% 8%, 15% 12%, 25% 18%, 35% 22%, 45% 28%, 55% 32%, 65% 38%, 75% 42%, 85% 48%, 95% 52%, 8% 15%, 18% 25%, 28% 35%, 38% 45%, 48% 55%, 58% 65%, 68% 75%, 78% 85%, 88% 95%, 98% 5%, 12% 62%, 22% 72%, 32% 82%, 42% 92%, 52% 2%, 62% 12%, 72% 22%, 82% 32%, 92% 42%, 2% 52%, 7% 77%, 17% 87%, 27% 97%, 37% 7%, 47% 17%, 57% 27%, 67% 37%, 77% 47%, 87% 57%, 97% 67%, 3% 33%, 13% 43%, 23% 53%, 33% 63%, 43% 73%, 53% 83%, 63% 93%, 73% 3%, 83% 13%, 93% 23%, 9% 59%, 19% 69%, 29% 79%; }
25% { background-position: 67% 23%, 89% 67%, 12% 89%, 45% 12%, 78% 45%, 23% 78%, 56% 23%, 89% 56%, 12% 89%, 45% 12%, 78% 45%, 23% 78%, 56% 23%, 89% 56%, 12% 89%, 45% 12%, 78% 45%, 23% 78%, 56% 23%, 89% 56%, 34% 67%, 67% 34%, 90% 67%, 23% 90%, 56% 23%, 89% 56%, 12% 89%, 45% 12%, 78% 45%, 23% 78%, 56% 23%, 89% 56%, 12% 89%, 45% 12%, 78% 45%, 23% 78%, 56% 23%, 89% 56%, 12% 89%, 45% 12%, 78% 45%, 23% 78%, 56% 23%, 89% 56%, 12% 89%, 45% 12%, 78% 45%, 23% 78%, 56% 23%, 89% 56%, 12% 89%, 45% 12%, 78% 45%; }
50% { background-position: 89% 45%, 34% 89%, 67% 34%, 90% 67%, 23% 90%, 56% 23%, 89% 56%, 12% 89%, 45% 12%, 78% 45%, 23% 78%, 56% 23%, 89% 56%, 12% 89%, 45% 12%, 78% 45%, 23% 78%, 56% 23%, 89% 56%, 12% 89%, 78% 34%, 12% 78%, 45% 12%, 78% 45%, 23% 78%, 56% 23%, 89% 56%, 12% 89%, 45% 12%, 78% 45%, 23% 78%, 56% 23%, 89% 56%, 12% 89%, 45% 12%, 78% 45%, 23% 78%, 56% 23%, 89% 56%, 12% 89%, 45% 12%, 78% 45%, 23% 78%, 56% 23%, 89% 56%, 12% 89%, 45% 12%, 78% 45%, 23% 78%, 56% 23%, 89% 56%, 12% 89%, 45% 12%; }
75% { background-position: 23% 78%, 56% 23%, 89% 56%, 12% 89%, 45% 12%, 78% 45%, 23% 78%, 56% 23%, 89% 56%, 12% 89%, 45% 12%, 78% 45%, 23% 78%, 56% 23%, 89% 56%, 12% 89%, 45% 12%, 78% 45%, 23% 78%, 56% 23%, 45% 89%, 78% 45%, 23% 78%, 56% 23%, 89% 56%, 12% 89%, 45% 12%, 78% 45%, 23% 78%, 56% 23%, 89% 56%, 12% 89%, 45% 12%, 78% 45%, 23% 78%, 56% 23%, 89% 56%, 12% 89%, 45% 12%, 78% 45%, 23% 78%, 56% 23%, 89% 56%, 12% 89%, 45% 12%, 78% 45%, 23% 78%, 56% 23%, 89% 56%, 12% 89%, 45% 12%, 78% 45%; }
100% { background-position: 105% 108%, 115% 112%, 125% 118%, 135% 122%, 145% 128%, 155% 132%, 165% 138%, 175% 142%, 185% 148%, 195% 152%, 108% 115%, 118% 125%, 128% 135%, 138% 145%, 148% 155%, 158% 165%, 168% 175%, 178% 185%, 188% 195%, 198% 105%, 112% 162%, 122% 172%, 132% 182%, 142% 192%, 152% 102%, 162% 112%, 172% 122%, 182% 132%, 192% 142%, 102% 152%, 107% 177%, 117% 187%, 127% 197%, 137% 107%, 147% 117%, 157% 127%, 167% 137%, 177% 147%, 187% 157%, 197% 167%, 103% 133%, 113% 143%, 123% 153%, 133% 163%, 143% 173%, 153% 183%, 163% 193%, 173% 103%, 183% 113%, 193% 123%, 109% 159%, 119% 169%, 129% 179%; }
}

@keyframes particle-random-2 {
0% { background-position: 5% 8%, 15% 12%, 25% 18%, 35% 22%, 45% 28%, 55% 32%, 65% 38%, 75% 42%, 85% 48%, 95% 52%, 8% 15%, 18% 25%, 28% 35%, 38% 45%, 48% 55%, 58% 65%, 68% 75%, 78% 85%, 88% 95%, 98% 5%, 12% 62%, 22% 72%, 32% 82%, 42% 92%, 52% 2%, 62% 12%, 72% 22%, 82% 32%, 92% 42%, 2% 52%, 7% 77%, 17% 87%, 27% 97%, 37% 7%, 47% 17%, 57% 27%, 67% 37%, 77% 47%, 87% 57%, 97% 67%, 3% 33%, 13% 43%, 23% 53%, 33% 63%, 43% 73%, 53% 83%, 63% 93%, 73% 3%, 83% 13%, 93% 23%, 9% 59%, 19% 69%, 29% 79%; }
25% { background-position: 78% 34%, 23% 78%, 67% 23%, 12% 67%, 56% 12%, 90% 56%, 34% 90%, 78% 34%, 23% 78%, 67% 23%, 12% 67%, 56% 12%, 90% 56%, 34% 90%, 78% 34%, 23% 78%, 67% 23%, 12% 67%, 56% 12%, 90% 56%, 45% 78%, 89% 45%, 34% 89%, 78% 34%, 23% 78%, 67% 23%, 12% 67%, 56% 12%, 90% 56%, 34% 90%, 78% 34%, 23% 78%, 67% 23%, 12% 67%, 56% 12%, 90% 56%, 34% 90%, 78% 34%, 23% 78%, 67% 23%, 12% 67%, 56% 12%, 90% 56%, 34% 90%, 78% 34%, 23% 78%, 67% 23%, 12% 67%, 56% 12%, 90% 56%, 34% 90%, 78% 34%, 23% 78%; }
50% { background-position: 12% 67%, 56% 12%, 90% 56%, 34% 90%, 78% 34%, 23% 78%, 67% 23%, 12% 67%, 56% 12%, 90% 56%, 34% 90%, 78% 34%, 23% 78%, 67% 23%, 12% 67%, 56% 12%, 90% 56%, 34% 90%, 78% 34%, 23% 78%, 56% 90%, 90% 56%, 34% 90%, 78% 34%, 23% 78%, 67% 23%, 12% 67%, 56% 12%, 90% 56%, 34% 90%, 78% 34%, 23% 78%, 67% 23%, 12% 67%, 56% 12%, 90% 56%, 34% 90%, 78% 34%, 23% 78%, 67% 23%, 12% 67%, 56% 12%, 90% 56%, 34% 90%, 78% 34%, 23% 78%, 67% 23%, 12% 67%, 56% 12%, 90% 56%, 34% 90%, 78% 34%; }
75% { background-position: 90% 56%, 34% 90%, 78% 34%, 23% 78%, 67% 23%, 12% 67%, 56% 12%, 90% 56%, 34% 90%, 78% 34%, 23% 78%, 67% 23%, 12% 67%, 56% 12%, 90% 56%, 34% 90%, 78% 34%, 23% 78%, 67% 23%, 12% 67%, 67% 34%, 12% 67%, 56% 12%, 90% 56%, 34% 90%, 78% 34%, 23% 78%, 67% 23%, 12% 67%, 56% 12%, 90% 56%, 34% 90%, 78% 34%, 23% 78%, 67% 23%, 12% 67%, 56% 12%, 90% 56%, 34% 90%, 78% 34%, 23% 78%, 67% 23%, 12% 67%, 56% 12%, 90% 56%, 34% 90%, 78% 34%, 23% 78%, 67% 23%, 12% 67%, 56% 12%, 90% 56%; }
100% { background-position: -95% -92%, -85% -88%, -75% -82%, -65% -78%, -55% -72%, -45% -68%, -35% -62%, -25% -58%, -15% -52%, -5% -48%, -92% -85%, -82% -75%, -72% -65%, -62% -55%, -52% -45%, -42% -35%, -32% -25%, -22% -15%, -12% -5%, -2% -95%, -88% -38%, -78% -28%, -68% -18%, -58% -8%, -48% -98%, -38% -88%, -28% -78%, -18% -68%, -8% -58%, -98% -48%, -93% -23%, -83% -13%, -73% -3%, -63% -93%, -53% -83%, -43% -73%, -33% -63%, -23% -53%, -13% -43%, -3% -33%, -97% -67%, -87% -57%, -77% -47%, -67% -37%, -57% -27%, -47% -17%, -37% -7%, -27% -97%, -17% -87%, -7% -77%, -91% -41%, -81% -31%, -71% -21%; }
}

/* 7. 波浪运动轨迹 */
@keyframes particle-wave-1 {
0% { background-position: 5% 50%, 15% 50%, 25% 50%, 35% 50%, 45% 50%, 55% 50%, 65% 50%, 75% 50%, 85% 50%, 95% 50%, 8% 50%, 18% 50%, 28% 50%, 38% 50%, 48% 50%, 58% 50%, 68% 50%, 78% 50%, 88% 50%, 98% 50%, 12% 50%, 22% 50%, 32% 50%, 42% 50%, 52% 50%, 62% 50%, 72% 50%, 82% 50%, 92% 50%, 2% 50%, 7% 50%, 17% 50%, 27% 50%, 37% 50%, 47% 50%, 57% 50%, 67% 50%, 77% 50%, 87% 50%, 97% 50%, 3% 50%, 13% 50%, 23% 50%, 33% 50%, 43% 50%, 53% 50%, 63% 50%, 73% 50%, 83% 50%, 93% 50%, 9% 50%, 19% 50%, 29% 50%; }
25% { background-position: 25% 30%, 35% 70%, 45% 30%, 55% 70%, 65% 30%, 75% 70%, 85% 30%, 95% 70%, 105% 30%, 115% 70%, 28% 30%, 38% 70%, 48% 30%, 58% 70%, 68% 30%, 78% 70%, 88% 30%, 98% 70%, 108% 30%, 118% 70%, 32% 30%, 42% 70%, 52% 30%, 62% 70%, 72% 30%, 82% 70%, 92% 30%, 102% 70%, 112% 30%, 22% 70%, 27% 30%, 37% 70%, 47% 30%, 57% 70%, 67% 30%, 77% 70%, 87% 30%, 97% 70%, 107% 30%, 117% 70%, 23% 30%, 33% 70%, 43% 30%, 53% 70%, 63% 30%, 73% 70%, 83% 30%, 93% 70%, 103% 30%, 113% 70%, 29% 30%, 39% 70%, 49% 30%; }
50% { background-position: 45% 20%, 55% 80%, 65% 20%, 75% 80%, 85% 20%, 95% 80%, 105% 20%, 115% 80%, 125% 20%, 135% 80%, 48% 20%, 58% 80%, 68% 20%, 78% 80%, 88% 20%, 98% 80%, 108% 20%, 118% 80%, 128% 20%, 138% 80%, 52% 20%, 62% 80%, 72% 20%, 82% 80%, 92% 20%, 102% 80%, 112% 20%, 122% 80%, 132% 20%, 42% 80%, 47% 20%, 57% 80%, 67% 20%, 77% 80%, 87% 20%, 97% 80%, 107% 20%, 117% 80%, 127% 20%, 137% 80%, 43% 20%, 53% 80%, 63% 20%, 73% 80%, 83% 20%, 93% 80%, 103% 20%, 113% 80%, 123% 20%, 133% 80%, 49% 20%, 59% 80%, 69% 20%; }
75% { background-position: 65% 30%, 75% 70%, 85% 30%, 95% 70%, 105% 30%, 115% 70%, 125% 30%, 135% 70%, 145% 30%, 155% 70%, 68% 30%, 78% 70%, 88% 30%, 98% 70%, 108% 30%, 118% 70%, 128% 30%, 138% 70%, 148% 30%, 158% 70%, 72% 30%, 82% 70%, 92% 30%, 102% 70%, 112% 30%, 122% 70%, 132% 30%, 142% 70%, 152% 30%, 62% 70%, 67% 30%, 77% 70%, 87% 30%, 97% 70%, 107% 30%, 117% 70%, 127% 30%, 137% 70%, 147% 30%, 157% 70%, 63% 30%, 73% 70%, 83% 30%, 93% 70%, 103% 30%, 113% 70%, 123% 30%, 133% 70%, 143% 30%, 153% 70%, 69% 30%, 79% 70%, 89% 30%; }
100% { background-position: 85% 50%, 95% 50%, 105% 50%, 115% 50%, 125% 50%, 135% 50%, 145% 50%, 155% 50%, 165% 50%, 175% 50%, 88% 50%, 98% 50%, 108% 50%, 118% 50%, 128% 50%, 138% 50%, 148% 50%, 158% 50%, 168% 50%, 178% 50%, 92% 50%, 102% 50%, 112% 50%, 122% 50%, 132% 50%, 142% 50%, 152% 50%, 162% 50%, 172% 50%, 82% 50%, 87% 50%, 97% 50%, 107% 50%, 117% 50%, 127% 50%, 137% 50%, 147% 50%, 157% 50%, 167% 50%, 177% 50%, 83% 50%, 93% 50%, 103% 50%, 113% 50%, 123% 50%, 133% 50%, 143% 50%, 153% 50%, 163% 50%, 173% 50%, 89% 50%, 99% 50%, 109% 50%; }
}

@keyframes particle-wave-2 {
0% { background-position: 5% 50%, 15% 50%, 25% 50%, 35% 50%, 45% 50%, 55% 50%, 65% 50%, 75% 50%, 85% 50%, 95% 50%, 8% 50%, 18% 50%, 28% 50%, 38% 50%, 48% 50%, 58% 50%, 68% 50%, 78% 50%, 88% 50%, 98% 50%, 12% 50%, 22% 50%, 32% 50%, 42% 50%, 52% 50%, 62% 50%, 72% 50%, 82% 50%, 92% 50%, 2% 50%, 7% 50%, 17% 50%, 27% 50%, 37% 50%, 47% 50%, 57% 50%, 67% 50%, 77% 50%, 87% 50%, 97% 50%, 3% 50%, 13% 50%, 23% 50%, 33% 50%, 43% 50%, 53% 50%, 63% 50%, 73% 50%, 83% 50%, 93% 50%, 9% 50%, 19% 50%, 29% 50%; }
25% { background-position: 25% 70%, 35% 30%, 45% 70%, 55% 30%, 65% 70%, 75% 30%, 85% 70%, 95% 30%, 105% 70%, 115% 30%, 28% 70%, 38% 30%, 48% 70%, 58% 30%, 68% 70%, 78% 30%, 88% 70%, 98% 30%, 108% 70%, 118% 30%, 32% 70%, 42% 30%, 52% 70%, 62% 30%, 72% 70%, 82% 30%, 92% 70%, 102% 30%, 112% 70%, 22% 30%, 27% 70%, 37% 30%, 47% 70%, 57% 30%, 67% 70%, 77% 30%, 87% 70%, 97% 30%, 107% 70%, 117% 30%, 23% 70%, 33% 30%, 43% 70%, 53% 30%, 63% 70%, 73% 30%, 83% 70%, 93% 30%, 103% 70%, 113% 30%, 29% 70%, 39% 30%, 49% 70%; }
50% { background-position: 45% 80%, 55% 20%, 65% 80%, 75% 20%, 85% 80%, 95% 20%, 105% 80%, 115% 20%, 125% 80%, 135% 20%, 48% 80%, 58% 20%, 68% 80%, 78% 20%, 88% 80%, 98% 20%, 108% 80%, 118% 20%, 128% 80%, 138% 20%, 52% 80%, 62% 20%, 72% 80%, 82% 20%, 92% 80%, 102% 20%, 112% 80%, 122% 20%, 132% 80%, 42% 20%, 47% 80%, 57% 20%, 67% 80%, 77% 20%, 87% 80%, 97% 20%, 107% 80%, 117% 20%, 127% 80%, 137% 20%, 43% 80%, 53% 20%, 63% 80%, 73% 20%, 83% 80%, 93% 20%, 103% 80%, 113% 20%, 123% 80%, 133% 20%, 49% 80%, 59% 20%, 69% 80%; }
75% { background-position: 65% 70%, 75% 30%, 85% 70%, 95% 30%, 105% 70%, 115% 30%, 125% 70%, 135% 30%, 145% 70%, 155% 30%, 68% 70%, 78% 30%, 88% 70%, 98% 30%, 108% 70%, 118% 30%, 128% 70%, 138% 30%, 148% 70%, 158% 30%, 72% 70%, 82% 30%, 92% 70%, 102% 30%, 112% 70%, 122% 30%, 132% 70%, 142% 30%, 152% 70%, 62% 30%, 67% 70%, 77% 30%, 87% 70%, 97% 30%, 107% 70%, 117% 30%, 127% 70%, 137% 30%, 147% 70%, 157% 30%, 63% 70%, 73% 30%, 83% 70%, 93% 30%, 103% 70%, 113% 30%, 123% 70%, 133% 30%, 143% 70%, 153% 30%, 69% 70%, 79% 30%, 89% 70%; }
100% { background-position: 85% 50%, 95% 50%, 105% 50%, 115% 50%, 125% 50%, 135% 50%, 145% 50%, 155% 50%, 165% 50%, 175% 50%, 88% 50%, 98% 50%, 108% 50%, 118% 50%, 128% 50%, 138% 50%, 148% 50%, 158% 50%, 168% 50%, 178% 50%, 92% 50%, 102% 50%, 112% 50%, 122% 50%, 132% 50%, 142% 50%, 152% 50%, 162% 50%, 172% 50%, 82% 50%, 87% 50%, 97% 50%, 107% 50%, 117% 50%, 127% 50%, 137% 50%, 147% 50%, 157% 50%, 167% 50%, 177% 50%, 83% 50%, 93% 50%, 103% 50%, 113% 50%, 123% 50%, 133% 50%, 143% 50%, 153% 50%, 163% 50%, 173% 50%, 89% 50%, 99% 50%, 109% 50%; }
}

/* 8. 脉冲运动轨迹 */
@keyframes particle-pulse-1 {
0% { background-position: 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%; }
25% { background-position: 60% 40%, 40% 60%, 70% 30%, 30% 70%, 80% 20%, 20% 80%, 90% 10%, 10% 90%, 100% 0%, 0% 100%, 55% 45%, 45% 55%, 65% 35%, 35% 65%, 75% 25%, 25% 75%, 85% 15%, 15% 85%, 95% 5%, 5% 95%, 105% -5%, -5% 105%, 52% 48%, 48% 52%, 62% 38%, 38% 62%, 72% 28%, 28% 72%, 82% 18%, 18% 82%, 92% 8%, 8% 92%, 102% -2%, -2% 102%, 57% 43%, 43% 57%, 67% 33%, 33% 67%, 77% 23%, 23% 77%, 87% 13%, 13% 87%, 97% 3%, 3% 97%, 107% -7%, -7% 107%, 53% 47%, 47% 53%, 63% 37%, 37% 63%, 73% 27%, 27% 73%, 83% 17%, 17% 83%, 93% 7%, 7% 93%, 103% -3%, -3% 103%; }
50% { background-position: 70% 30%, 30% 70%, 90% 10%, 10% 90%, 110% -10%, -10% 110%, 130% -30%, -30% 130%, 150% -50%, -50% 150%, 65% 35%, 35% 65%, 85% 15%, 15% 85%, 105% -5%, -5% 105%, 125% -25%, -25% 125%, 145% -45%, -45% 145%, 165% -65%, -65% 165%, 62% 38%, 38% 62%, 82% 18%, 18% 82%, 102% -2%, -2% 102%, 122% -22%, -22% 122%, 142% -42%, -42% 142%, 162% -62%, -62% 162%, 67% 33%, 33% 67%, 87% 13%, 13% 87%, 107% -7%, -7% 107%, 127% -27%, -27% 127%, 147% -47%, -47% 147%, 167% -67%, -67% 167%, 63% 37%, 37% 63%, 83% 17%, 17% 83%, 103% -3%, -3% 103%, 123% -23%, -23% 123%, 143% -43%, -43% 143%, 163% -63%, -63% 163%; }
75% { background-position: 60% 40%, 40% 60%, 70% 30%, 30% 70%, 80% 20%, 20% 80%, 90% 10%, 10% 90%, 100% 0%, 0% 100%, 55% 45%, 45% 55%, 65% 35%, 35% 65%, 75% 25%, 25% 75%, 85% 15%, 15% 85%, 95% 5%, 5% 95%, 105% -5%, -5% 105%, 52% 48%, 48% 52%, 62% 38%, 38% 62%, 72% 28%, 28% 72%, 82% 18%, 18% 82%, 92% 8%, 8% 92%, 102% -2%, -2% 102%, 57% 43%, 43% 57%, 67% 33%, 33% 67%, 77% 23%, 23% 77%, 87% 13%, 13% 87%, 97% 3%, 3% 97%, 107% -7%, -7% 107%, 53% 47%, 47% 53%, 63% 37%, 37% 63%, 73% 27%, 27% 73%, 83% 17%, 17% 83%, 93% 7%, 7% 93%, 103% -3%, -3% 103%; }
100% { background-position: 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%; }
}

@keyframes particle-pulse-2 {
0% { background-position: 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%; }
20% { background-position: 55% 45%, 45% 55%, 60% 40%, 40% 60%, 65% 35%, 35% 65%, 70% 30%, 30% 70%, 75% 25%, 25% 75%, 52% 48%, 48% 52%, 57% 43%, 43% 57%, 62% 38%, 38% 62%, 67% 33%, 33% 67%, 72% 28%, 28% 72%, 53% 47%, 47% 53%, 58% 42%, 42% 58%, 63% 37%, 37% 63%, 68% 32%, 32% 68%, 73% 27%, 27% 73%, 54% 46%, 46% 54%, 59% 41%, 41% 59%, 64% 36%, 36% 64%, 69% 31%, 31% 69%, 74% 26%, 26% 74%, 51% 49%, 49% 51%, 56% 44%, 44% 56%, 61% 39%, 39% 61%, 66% 34%, 34% 66%, 71% 29%, 29% 71%, 52% 48%, 48% 52%, 57% 43%, 43% 57%, 62% 38%, 38% 62%, 67% 33%, 33% 67%, 72% 28%, 28% 72%; }
40% { background-position: 60% 40%, 40% 60%, 70% 30%, 30% 70%, 80% 20%, 20% 80%, 90% 10%, 10% 90%, 100% 0%, 0% 100%, 57% 43%, 43% 57%, 67% 33%, 33% 67%, 77% 23%, 23% 77%, 87% 13%, 13% 87%, 97% 3%, 3% 97%, 58% 42%, 42% 58%, 68% 32%, 32% 68%, 78% 22%, 22% 78%, 88% 12%, 12% 88%, 98% 2%, 2% 98%, 59% 41%, 41% 59%, 69% 31%, 31% 69%, 79% 21%, 21% 79%, 89% 11%, 11% 89%, 99% 1%, 1% 99%, 56% 44%, 44% 56%, 66% 34%, 34% 66%, 76% 24%, 24% 76%, 86% 14%, 14% 86%, 96% 4%, 4% 96%, 57% 43%, 43% 57%, 67% 33%, 33% 67%, 77% 23%, 23% 77%, 87% 13%, 13% 87%, 97% 3%, 3% 97%; }
60% { background-position: 65% 35%, 35% 65%, 80% 20%, 20% 80%, 95% 5%, 5% 95%, 110% -10%, -10% 110%, 125% -25%, -25% 125%, 62% 38%, 38% 62%, 77% 23%, 23% 77%, 92% 8%, 8% 92%, 107% -7%, -7% 107%, 122% -22%, -22% 122%, 63% 37%, 37% 63%, 78% 22%, 22% 78%, 93% 7%, 7% 93%, 108% -8%, -8% 108%, 123% -23%, -23% 123%, 64% 36%, 36% 64%, 79% 21%, 21% 79%, 94% 6%, 6% 94%, 109% -9%, -9% 109%, 124% -24%, -24% 124%, 61% 39%, 39% 61%, 76% 24%, 24% 76%, 91% 9%, 9% 91%, 106% -6%, -6% 106%, 121% -21%, -21% 121%, 62% 38%, 38% 62%, 77% 23%, 23% 77%, 92% 8%, 8% 92%, 107% -7%, -7% 107%, 122% -22%, -22% 122%; }
80% { background-position: 55% 45%, 45% 55%, 60% 40%, 40% 60%, 65% 35%, 35% 65%, 70% 30%, 30% 70%, 75% 25%, 25% 75%, 52% 48%, 48% 52%, 57% 43%, 43% 57%, 62% 38%, 38% 62%, 67% 33%, 33% 67%, 72% 28%, 28% 72%, 53% 47%, 47% 53%, 58% 42%, 42% 58%, 63% 37%, 37% 63%, 68% 32%, 32% 68%, 73% 27%, 27% 73%, 54% 46%, 46% 54%, 59% 41%, 41% 59%, 64% 36%, 36% 64%, 69% 31%, 31% 69%, 74% 26%, 26% 74%, 51% 49%, 49% 51%, 56% 44%, 44% 56%, 61% 39%, 39% 61%, 66% 34%, 34% 66%, 71% 29%, 29% 71%, 52% 48%, 48% 52%, 57% 43%, 43% 57%, 62% 38%, 38% 62%, 67% 33%, 33% 67%, 72% 28%, 28% 72%; }
100% { background-position: 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%, 50% 50%; }
}

/* 9. 轨道运动轨迹 */
@keyframes particle-orbit-1 {
0% { background-position: 50% 30%, 70% 50%, 50% 70%, 30% 50%, 60% 40%, 60% 60%, 40% 60%, 40% 40%, 55% 35%, 65% 55%, 45% 65%, 35% 45%, 75% 50%, 50% 75%, 25% 50%, 50% 25%, 80% 50%, 50% 80%, 20% 50%, 50% 20%, 85% 50%, 50% 85%, 15% 50%, 50% 15%, 90% 50%, 50% 90%, 10% 50%, 50% 10%, 95% 50%, 50% 95%, 5% 50%, 50% 5%, 100% 50%, 50% 100%, 0% 50%, 50% 0%, 105% 50%, 50% 105%, -5% 50%, 50% -5%, 110% 50%, 50% 110%, -10% 50%, 50% -10%, 115% 50%, 50% 115%, -15% 50%, 50% -15%; }
25% { background-position: 70% 30%, 70% 70%, 30% 70%, 30% 30%, 80% 40%, 60% 80%, 20% 60%, 40% 20%, 75% 35%, 65% 75%, 25% 65%, 35% 25%, 95% 50%, 50% 95%, 5% 50%, 50% 5%, 100% 50%, 50% 100%, 0% 50%, 50% 0%, 105% 50%, 50% 105%, -5% 50%, 50% -5%, 110% 50%, 50% 110%, -10% 50%, 50% -10%, 115% 50%, 50% 115%, -15% 50%, 50% -15%, 120% 50%, 50% 120%, -20% 50%, 50% -20%, 125% 50%, 50% 125%, -25% 50%, 50% -25%, 130% 50%, 50% 130%, -30% 50%, 50% -30%, 135% 50%, 50% 135%, -35% 50%, 50% -35%; }
50% { background-position: 70% 50%, 50% 70%, 30% 50%, 50% 30%, 80% 60%, 40% 80%, 20% 40%, 60% 20%, 75% 55%, 45% 75%, 25% 45%, 55% 25%, 95% 75%, 25% 95%, 5% 25%, 75% 5%, 100% 80%, 20% 100%, 0% 20%, 80% 0%, 105% 85%, 15% 105%, -5% 15%, 85% -5%, 110% 90%, 10% 110%, -10% 10%, 90% -10%, 115% 95%, 5% 115%, -15% 5%, 95% -15%, 120% 100%, 0% 120%, -20% 0%, 100% -20%, 125% 105%, -5% 125%, -25% -5%, 105% -25%, 130% 110%, -10% 130%, -30% -10%, 110% -30%, 135% 115%, -15% 135%, -35% -15%, 115% -35%; }
75% { background-position: 50% 70%, 30% 50%, 50% 30%, 70% 50%, 60% 80%, 20% 60%, 40% 20%, 80% 40%, 55% 75%, 25% 55%, 45% 25%, 75% 45%, 75% 95%, 5% 75%, 25% 5%, 95% 25%, 80% 100%, 0% 80%, 20% 0%, 100% 20%, 85% 105%, -5% 85%, 15% -5%, 105% 15%, 90% 110%, -10% 90%, 10% -10%, 110% 10%, 95% 115%, -15% 95%, 5% -15%, 115% 5%, 100% 120%, -20% 100%, 0% -20%, 120% 0%, 105% 125%, -25% 105%, -5% -25%, 125% -5%, 110% 130%, -30% 110%, -10% -30%, 130% -10%, 115% 135%, -35% 115%, -15% -35%, 135% -15%; }
100% { background-position: 30% 50%, 50% 30%, 70% 50%, 50% 70%, 40% 60%, 60% 40%, 40% 60%, 60% 40%, 35% 55%, 55% 35%, 45% 65%, 65% 45%, 50% 75%, 75% 50%, 25% 75%, 25% 50%, 50% 80%, 80% 50%, 20% 80%, 20% 50%, 50% 85%, 85% 50%, 15% 85%, 15% 50%, 50% 90%, 90% 50%, 10% 90%, 10% 50%, 50% 95%, 95% 50%, 5% 95%, 5% 50%, 50% 100%, 100% 50%, 0% 100%, 0% 50%, 50% 105%, 105% 50%, -5% 105%, -5% 50%, 50% 110%, 110% 50%, -10% 110%, -10% 50%, 50% 115%, 115% 50%, -15% 115%, -15% 50%; }
}

@keyframes particle-orbit-2 {
0% { background-position: 50% 20%, 80% 50%, 50% 80%, 20% 50%, 70% 30%, 70% 70%, 30% 70%, 30% 30%, 65% 25%, 75% 65%, 25% 75%, 35% 25%, 85% 50%, 50% 85%, 15% 50%, 50% 15%, 90% 50%, 50% 90%, 10% 50%, 50% 10%, 95% 50%, 50% 95%, 5% 50%, 50% 5%, 100% 50%, 50% 100%, 0% 50%, 50% 0%, 105% 50%, 50% 105%, -5% 50%, 50% -5%, 110% 50%, 50% 110%, -10% 50%, 50% -10%, 115% 50%, 50% 115%, -15% 50%, 50% -15%, 120% 50%, 50% 120%, -20% 50%, 50% -20%, 125% 50%, 50% 125%, -25% 50%, 50% -25%; }
25% { background-position: 80% 20%, 80% 80%, 20% 80%, 20% 20%, 90% 30%, 70% 90%, 10% 70%, 30% 10%, 85% 25%, 75% 85%, 15% 75%, 25% 15%, 105% 50%, 50% 105%, -5% 50%, 50% -5%, 110% 50%, 50% 110%, -10% 50%, 50% -10%, 115% 50%, 50% 115%, -15% 50%, 50% -15%, 120% 50%, 50% 120%, -20% 50%, 50% -20%, 125% 50%, 50% 125%, -25% 50%, 50% -25%, 130% 50%, 50% 130%, -30% 50%, 50% -30%, 135% 50%, 50% 135%, -35% 50%, 50% -35%, 140% 50%, 50% 140%, -40% 50%, 50% -40%, 145% 50%, 50% 145%, -45% 50%, 50% -45%; }
50% { background-position: 80% 50%, 50% 80%, 20% 50%, 50% 20%, 90% 70%, 30% 90%, 10% 30%, 70% 10%, 85% 65%, 35% 85%, 15% 35%, 65% 15%, 105% 85%, 15% 105%, -5% 15%, 85% -5%, 110% 90%, 10% 110%, -10% 10%, 90% -10%, 115% 95%, 5% 115%, -15% 5%, 95% -15%, 120% 100%, 0% 120%, -20% 0%, 100% -20%, 125% 105%, -5% 125%, -25% -5%, 105% -25%, 130% 110%, -10% 130%, -30% -10%, 110% -30%, 135% 115%, -15% 135%, -35% -15%, 115% -35%, 140% 120%, -20% 140%, -40% -20%, 120% -40%, 145% 125%, -25% 145%, -45% -25%, 125% -45%; }
75% { background-position: 50% 80%, 20% 50%, 50% 20%, 80% 50%, 70% 90%, 10% 70%, 30% 10%, 90% 30%, 65% 85%, 15% 65%, 35% 15%, 85% 35%, 85% 105%, -5% 85%, 15% -5%, 105% 15%, 90% 110%, -10% 90%, 10% -10%, 110% 10%, 95% 115%, -15% 95%, 5% -15%, 115% 5%, 100% 120%, -20% 100%, 0% -20%, 120% 0%, 105% 125%, -25% 105%, -5% -25%, 125% -5%, 110% 130%, -30% 110%, -10% -30%, 130% -10%, 115% 135%, -35% 115%, -15% -35%, 135% -15%, 120% 140%, -40% 120%, -20% -40%, 140% -20%, 125% 145%, -45% 125%, -25% -45%, 145% -25%; }
100% { background-position: 20% 50%, 50% 20%, 80% 50%, 50% 80%, 30% 70%, 70% 30%, 30% 70%, 70% 30%, 25% 65%, 65% 25%, 35% 75%, 75% 35%, 50% 85%, 85% 50%, 15% 85%, 15% 50%, 50% 90%, 90% 50%, 10% 90%, 10% 50%, 50% 95%, 95% 50%, 5% 95%, 5% 50%, 50% 100%, 100% 50%, 0% 100%, 0% 50%, 50% 105%, 105% 50%, -5% 105%, -5% 50%, 50% 110%, 110% 50%, -10% 110%, -10% 50%, 50% 115%, 115% 50%, -15% 115%, -15% 50%, 50% 120%, 120% 50%, -20% 120%, -20% 50%, 50% 125%, 125% 50%, -25% 125%, -25% 50%; }
}

/* 10. Z字形运动轨迹 */
@keyframes particle-zigzag-1 {
0% { background-position: 5% 50%, 15% 50%, 25% 50%, 35% 50%, 45% 50%, 55% 50%, 65% 50%, 75% 50%, 85% 50%, 95% 50%, 8% 50%, 18% 50%, 28% 50%, 38% 50%, 48% 50%, 58% 50%, 68% 50%, 78% 50%, 88% 50%, 98% 50%, 12% 50%, 22% 50%, 32% 50%, 42% 50%, 52% 50%, 62% 50%, 72% 50%, 82% 50%, 92% 50%, 2% 50%, 7% 50%, 17% 50%, 27% 50%, 37% 50%, 47% 50%, 57% 50%, 67% 50%, 77% 50%, 87% 50%, 97% 50%, 3% 50%, 13% 50%, 23% 50%, 33% 50%, 43% 50%, 53% 50%, 63% 50%, 73% 50%, 83% 50%, 93% 50%, 9% 50%, 19% 50%, 29% 50%; }
25% { background-position: 25% 30%, 35% 30%, 45% 30%, 55% 30%, 65% 30%, 75% 30%, 85% 30%, 95% 30%, 105% 30%, 115% 30%, 28% 30%, 38% 30%, 48% 30%, 58% 30%, 68% 30%, 78% 30%, 88% 30%, 98% 30%, 108% 30%, 118% 30%, 32% 30%, 42% 30%, 52% 30%, 62% 30%, 72% 30%, 82% 30%, 92% 30%, 102% 30%, 112% 30%, 22% 30%, 27% 30%, 37% 30%, 47% 30%, 57% 30%, 67% 30%, 77% 30%, 87% 30%, 97% 30%, 107% 30%, 117% 30%, 23% 30%, 33% 30%, 43% 30%, 53% 30%, 63% 30%, 73% 30%, 83% 30%, 93% 30%, 103% 30%, 113% 30%, 29% 30%, 39% 30%, 49% 30%; }
50% { background-position: 45% 70%, 55% 70%, 65% 70%, 75% 70%, 85% 70%, 95% 70%, 105% 70%, 115% 70%, 125% 70%, 135% 70%, 48% 70%, 58% 70%, 68% 70%, 78% 70%, 88% 70%, 98% 70%, 108% 70%, 118% 70%, 128% 70%, 138% 70%, 52% 70%, 62% 70%, 72% 70%, 82% 70%, 92% 70%, 102% 70%, 112% 70%, 122% 70%, 132% 70%, 42% 70%, 47% 70%, 57% 70%, 67% 70%, 77% 70%, 87% 70%, 97% 70%, 107% 70%, 117% 70%, 127% 70%, 137% 70%, 43% 70%, 53% 70%, 63% 70%, 73% 70%, 83% 70%, 93% 70%, 103% 70%, 113% 70%, 123% 70%, 133% 70%, 49% 70%, 59% 70%, 69% 70%; }
75% { background-position: 65% 30%, 75% 30%, 85% 30%, 95% 30%, 105% 30%, 115% 30%, 125% 30%, 135% 30%, 145% 30%, 155% 30%, 68% 30%, 78% 30%, 88% 30%, 98% 30%, 108% 30%, 118% 30%, 128% 30%, 138% 30%, 148% 30%, 158% 30%, 72% 30%, 82% 30%, 92% 30%, 102% 30%, 112% 30%, 122% 30%, 132% 30%, 142% 30%, 152% 30%, 62% 30%, 67% 30%, 77% 30%, 87% 30%, 97% 30%, 107% 30%, 117% 30%, 127% 30%, 137% 30%, 147% 30%, 157% 30%, 63% 30%, 73% 30%, 83% 30%, 93% 30%, 103% 30%, 113% 30%, 123% 30%, 133% 30%, 143% 30%, 153% 30%, 69% 30%, 79% 30%, 89% 30%; }
100% { background-position: 85% 50%, 95% 50%, 105% 50%, 115% 50%, 125% 50%, 135% 50%, 145% 50%, 155% 50%, 165% 50%, 175% 50%, 88% 50%, 98% 50%, 108% 50%, 118% 50%, 128% 50%, 138% 50%, 148% 50%, 158% 50%, 168% 50%, 178% 50%, 92% 50%, 102% 50%, 112% 50%, 122% 50%, 132% 50%, 142% 50%, 152% 50%, 162% 50%, 172% 50%, 82% 50%, 87% 50%, 97% 50%, 107% 50%, 117% 50%, 127% 50%, 137% 50%, 147% 50%, 157% 50%, 167% 50%, 177% 50%, 83% 50%, 93% 50%, 103% 50%, 113% 50%, 123% 50%, 133% 50%, 143% 50%, 153% 50%, 163% 50%, 173% 50%, 89% 50%, 99% 50%, 109% 50%; }
}

@keyframes particle-zigzag-2 {
0% { background-position: 5% 50%, 15% 50%, 25% 50%, 35% 50%, 45% 50%, 55% 50%, 65% 50%, 75% 50%, 85% 50%, 95% 50%, 8% 50%, 18% 50%, 28% 50%, 38% 50%, 48% 50%, 58% 50%, 68% 50%, 78% 50%, 88% 50%, 98% 50%, 12% 50%, 22% 50%, 32% 50%, 42% 50%, 52% 50%, 62% 50%, 72% 50%, 82% 50%, 92% 50%, 2% 50%, 7% 50%, 17% 50%, 27% 50%, 37% 50%, 47% 50%, 57% 50%, 67% 50%, 77% 50%, 87% 50%, 97% 50%, 3% 50%, 13% 50%, 23% 50%, 33% 50%, 43% 50%, 53% 50%, 63% 50%, 73% 50%, 83% 50%, 93% 50%, 9% 50%, 19% 50%, 29% 50%; }
25% { background-position: 25% 70%, 35% 70%, 45% 70%, 55% 70%, 65% 70%, 75% 70%, 85% 70%, 95% 70%, 105% 70%, 115% 70%, 28% 70%, 38% 70%, 48% 70%, 58% 70%, 68% 70%, 78% 70%, 88% 70%, 98% 70%, 108% 70%, 118% 70%, 32% 70%, 42% 70%, 52% 70%, 62% 70%, 72% 70%, 82% 70%, 92% 70%, 102% 70%, 112% 70%, 22% 70%, 27% 70%, 37% 70%, 47% 70%, 57% 70%, 67% 70%, 77% 70%, 87% 70%, 97% 70%, 107% 70%, 117% 70%, 23% 70%, 33% 70%, 43% 70%, 53% 70%, 63% 70%, 73% 70%, 83% 70%, 93% 70%, 103% 70%, 113% 70%, 29% 70%, 39% 70%, 49% 70%; }
50% { background-position: 45% 30%, 55% 30%, 65% 30%, 75% 30%, 85% 30%, 95% 30%, 105% 30%, 115% 30%, 125% 30%, 135% 30%, 48% 30%, 58% 30%, 68% 30%, 78% 30%, 88% 30%, 98% 30%, 108% 30%, 118% 30%, 128% 30%, 138% 30%, 52% 30%, 62% 30%, 72% 30%, 82% 30%, 92% 30%, 102% 30%, 112% 30%, 122% 30%, 132% 30%, 42% 30%, 47% 30%, 57% 30%, 67% 30%, 77% 30%, 87% 30%, 97% 30%, 107% 30%, 117% 30%, 127% 30%, 137% 30%, 43% 30%, 53% 30%, 63% 30%, 73% 30%, 83% 30%, 93% 30%, 103% 30%, 113% 30%, 123% 30%, 133% 30%, 49% 30%, 59% 30%, 69% 30%; }
75% { background-position: 65% 70%, 75% 70%, 85% 70%, 95% 70%, 105% 70%, 115% 70%, 125% 70%, 135% 70%, 145% 70%, 155% 70%, 68% 70%, 78% 70%, 88% 70%, 98% 70%, 108% 70%, 118% 70%, 128% 70%, 138% 70%, 148% 70%, 158% 70%, 72% 70%, 82% 70%, 92% 70%, 102% 70%, 112% 70%, 122% 70%, 132% 70%, 142% 70%, 152% 70%, 62% 70%, 67% 70%, 77% 70%, 87% 70%, 97% 70%, 107% 70%, 117% 70%, 127% 70%, 137% 70%, 147% 70%, 157% 70%, 63% 70%, 73% 70%, 83% 70%, 93% 70%, 103% 70%, 113% 70%, 123% 70%, 133% 70%, 143% 70%, 153% 70%, 69% 70%, 79% 70%, 89% 70%; }
100% { background-position: 85% 50%, 95% 50%, 105% 50%, 115% 50%, 125% 50%, 135% 50%, 145% 50%, 155% 50%, 165% 50%, 175% 50%, 88% 50%, 98% 50%, 108% 50%, 118% 50%, 128% 50%, 138% 50%, 148% 50%, 158% 50%, 168% 50%, 178% 50%, 92% 50%, 102% 50%, 112% 50%, 122% 50%, 132% 50%, 142% 50%, 152% 50%, 162% 50%, 172% 50%, 82% 50%, 87% 50%, 97% 50%, 107% 50%, 117% 50%, 127% 50%, 137% 50%, 147% 50%, 157% 50%, 167% 50%, 177% 50%, 83% 50%, 93% 50%, 103% 50%, 113% 50%, 123% 50%, 133% 50%, 143% 50%, 153% 50%, 163% 50%, 173% 50%, 89% 50%, 99% 50%, 109% 50%; }
}

/* 11. 漂移运动轨迹 */
@keyframes particle-drift-1 {
0% { background-position: 5% 8%, 15% 12%, 25% 18%, 35% 22%, 45% 28%, 55% 32%, 65% 38%, 75% 42%, 85% 48%, 95% 52%, 8% 15%, 18% 25%, 28% 35%, 38% 45%, 48% 55%, 58% 65%, 68% 75%, 78% 85%, 88% 95%, 98% 5%, 12% 62%, 22% 72%, 32% 82%, 42% 92%, 52% 2%, 62% 12%, 72% 22%, 82% 32%, 92% 42%, 2% 52%, 7% 77%, 17% 87%, 27% 97%, 37% 7%, 47% 17%, 57% 27%, 67% 37%, 77% 47%, 87% 57%, 97% 67%, 3% 33%, 13% 43%, 23% 53%, 33% 63%, 43% 73%, 53% 83%, 63% 93%, 73% 3%, 83% 13%, 93% 23%, 9% 59%, 19% 69%, 29% 79%; }
25% { background-position: 8% 15%, 18% 19%, 28% 25%, 38% 29%, 48% 35%, 58% 39%, 68% 45%, 78% 49%, 88% 55%, 98% 59%, 11% 22%, 21% 32%, 31% 42%, 41% 52%, 51% 62%, 61% 72%, 71% 82%, 81% 92%, 91% 2%, 101% 12%, 15% 69%, 25% 79%, 35% 89%, 45% 99%, 55% 9%, 65% 19%, 75% 29%, 85% 39%, 95% 49%, 5% 59%, 10% 84%, 20% 94%, 30% 4%, 40% 14%, 50% 24%, 60% 34%, 70% 44%, 80% 54%, 90% 64%, 100% 74%, 6% 40%, 16% 50%, 26% 60%, 36% 70%, 46% 80%, 56% 90%, 66% 0%, 76% 10%, 86% 20%, 96% 30%, 12% 66%, 22% 76%, 32% 86%; }
50% { background-position: 12% 22%, 22% 26%, 32% 32%, 42% 36%, 52% 42%, 62% 46%, 72% 52%, 82% 56%, 92% 62%, 102% 66%, 15% 29%, 25% 39%, 35% 49%, 45% 59%, 55% 69%, 65% 79%, 75% 89%, 85% 99%, 95% 9%, 105% 19%, 19% 76%, 29% 86%, 39% 96%, 49% 6%, 59% 16%, 69% 26%, 79% 36%, 89% 46%, 99% 56%, 9% 66%, 14% 91%, 24% 1%, 34% 11%, 44% 21%, 54% 31%, 64% 41%, 74% 51%, 84% 61%, 94% 71%, 104% 81%, 10% 47%, 20% 57%, 30% 67%, 40% 77%, 50% 87%, 60% 97%, 70% 7%, 80% 17%, 90% 27%, 100% 37%, 16% 73%, 26% 83%, 36% 93%; }
75% { background-position: 16% 29%, 26% 33%, 36% 39%, 46% 43%, 56% 49%, 66% 53%, 76% 59%, 86% 63%, 96% 69%, 106% 73%, 19% 36%, 29% 46%, 39% 56%, 49% 66%, 59% 76%, 69% 86%, 79% 96%, 89% 6%, 99% 16%, 109% 26%, 23% 83%, 33% 93%, 43% 3%, 53% 13%, 63% 23%, 73% 33%, 83% 43%, 93% 53%, 103% 63%, 13% 73%, 18% 98%, 28% 8%, 38% 18%, 48% 28%, 58% 38%, 68% 48%, 78% 58%, 88% 68%, 98% 78%, 108% 88%, 14% 54%, 24% 64%, 34% 74%, 44% 84%, 54% 94%, 64% 4%, 74% 14%, 84% 24%, 94% 34%, 104% 44%, 20% 80%, 30% 90%, 40% 0%; }
100% { background-position: 20% 36%, 30% 40%, 40% 46%, 50% 50%, 60% 56%, 70% 60%, 80% 66%, 90% 70%, 100% 76%, 110% 80%, 23% 43%, 33% 53%, 43% 63%, 53% 73%, 63% 83%, 73% 93%, 83% 3%, 93% 13%, 103% 23%, 113% 33%, 27% 90%, 37% 0%, 47% 10%, 57% 20%, 67% 30%, 77% 40%, 87% 50%, 97% 60%, 107% 70%, 17% 80%, 22% 5%, 32% 15%, 42% 25%, 52% 35%, 62% 45%, 72% 55%, 82% 65%, 92% 75%, 102% 85%, 112% 95%, 18% 61%, 28% 71%, 38% 81%, 48% 91%, 58% 1%, 68% 11%, 78% 21%, 88% 31%, 98% 41%, 108% 51%, 24% 87%, 34% 97%, 44% 7%; }
}

@keyframes particle-drift-2 {
0% { background-position: 5% 8%, 15% 12%, 25% 18%, 35% 22%, 45% 28%, 55% 32%, 65% 38%, 75% 42%, 85% 48%, 95% 52%, 8% 15%, 18% 25%, 28% 35%, 38% 45%, 48% 55%, 58% 65%, 68% 75%, 78% 85%, 88% 95%, 98% 5%, 12% 62%, 22% 72%, 32% 82%, 42% 92%, 52% 2%, 62% 12%, 72% 22%, 82% 32%, 92% 42%, 2% 52%, 7% 77%, 17% 87%, 27% 97%, 37% 7%, 47% 17%, 57% 27%, 67% 37%, 77% 47%, 87% 57%, 97% 67%, 3% 33%, 13% 43%, 23% 53%, 33% 63%, 43% 73%, 53% 83%, 63% 93%, 73% 3%, 83% 13%, 93% 23%, 9% 59%, 19% 69%, 29% 79%; }
25% { background-position: 7% 11%, 17% 15%, 27% 21%, 37% 25%, 47% 31%, 57% 35%, 67% 41%, 77% 45%, 87% 51%, 97% 55%, 10% 18%, 20% 28%, 30% 38%, 40% 48%, 50% 58%, 60% 68%, 70% 78%, 80% 88%, 90% 98%, 100% 8%, 14% 65%, 24% 75%, 34% 85%, 44% 95%, 54% 5%, 64% 15%, 74% 25%, 84% 35%, 94% 45%, 4% 55%, 9% 80%, 19% 90%, 29% 0%, 39% 10%, 49% 20%, 59% 30%, 69% 40%, 79% 50%, 89% 60%, 99% 70%, 5% 36%, 15% 46%, 25% 56%, 35% 66%, 45% 76%, 55% 86%, 65% 96%, 75% 6%, 85% 16%, 95% 26%, 11% 62%, 21% 72%, 31% 82%; }
50% { background-position: 9% 14%, 19% 18%, 29% 24%, 39% 28%, 49% 34%, 59% 38%, 69% 44%, 79% 48%, 89% 54%, 99% 58%, 12% 21%, 22% 31%, 32% 41%, 42% 51%, 52% 61%, 62% 71%, 72% 81%, 82% 91%, 92% 1%, 102% 11%, 16% 68%, 26% 78%, 36% 88%, 46% 98%, 56% 8%, 66% 18%, 76% 28%, 86% 38%, 96% 48%, 6% 58%, 11% 83%, 21% 93%, 31% 3%, 41% 13%, 51% 23%, 61% 33%, 71% 43%, 81% 53%, 91% 63%, 101% 73%, 7% 39%, 17% 49%, 27% 59%, 37% 69%, 47% 79%, 57% 89%, 67% 99%, 77% 9%, 87% 19%, 97% 29%, 13% 65%, 23% 75%, 33% 85%; }
75% { background-position: 11% 17%, 21% 21%, 31% 27%, 41% 31%, 51% 37%, 61% 41%, 71% 47%, 81% 51%, 91% 57%, 101% 61%, 14% 24%, 24% 34%, 34% 44%, 44% 54%, 54% 64%, 64% 74%, 74% 84%, 84% 94%, 94% 4%, 104% 14%, 18% 71%, 28% 81%, 38% 91%, 48% 1%, 58% 11%, 68% 21%, 78% 31%, 88% 41%, 98% 51%, 8% 61%, 13% 86%, 23% 96%, 33% 6%, 43% 16%, 53% 26%, 63% 36%, 73% 46%, 83% 56%, 93% 66%, 103% 76%, 9% 42%, 19% 52%, 29% 62%, 39% 72%, 49% 82%, 59% 92%, 69% 2%, 79% 12%, 89% 22%, 99% 32%, 15% 68%, 25% 78%, 35% 88%; }
100% { background-position: 13% 20%, 23% 24%, 33% 30%, 43% 34%, 53% 40%, 63% 44%, 73% 50%, 83% 54%, 93% 60%, 103% 64%, 16% 27%, 26% 37%, 36% 47%, 46% 57%, 56% 67%, 66% 77%, 76% 87%, 86% 97%, 96% 7%, 106% 17%, 20% 74%, 30% 84%, 40% 94%, 50% 4%, 60% 14%, 70% 24%, 80% 34%, 90% 44%, 100% 54%, 10% 64%, 15% 89%, 25% 99%, 35% 9%, 45% 19%, 55% 29%, 65% 39%, 75% 49%, 85% 59%, 95% 69%, 105% 79%, 11% 45%, 21% 55%, 31% 65%, 41% 75%, 51% 85%, 61% 95%, 71% 5%, 81% 15%, 91% 25%, 101% 35%, 17% 71%, 27% 81%, 37% 91%; }
}

/* 12. 弹跳运动轨迹 */
@keyframes particle-bounce-1 {
0% { background-position: 5% 50%, 15% 50%, 25% 50%, 35% 50%, 45% 50%, 55% 50%, 65% 50%, 75% 50%, 85% 50%, 95% 50%, 8% 50%, 18% 50%, 28% 50%, 38% 50%, 48% 50%, 58% 50%, 68% 50%, 78% 50%, 88% 50%, 98% 50%, 12% 50%, 22% 50%, 32% 50%, 42% 50%, 52% 50%, 62% 50%, 72% 50%, 82% 50%, 92% 50%, 2% 50%, 7% 50%, 17% 50%, 27% 50%, 37% 50%, 47% 50%, 57% 50%, 67% 50%, 77% 50%, 87% 50%, 97% 50%, 3% 50%, 13% 50%, 23% 50%, 33% 50%, 43% 50%, 53% 50%, 63% 50%, 73% 50%, 83% 50%, 93% 50%, 9% 50%, 19% 50%, 29% 50%; }
25% { background-position: 25% 20%, 35% 20%, 45% 20%, 55% 20%, 65% 20%, 75% 20%, 85% 20%, 95% 20%, 105% 20%, 115% 20%, 28% 20%, 38% 20%, 48% 20%, 58% 20%, 68% 20%, 78% 20%, 88% 20%, 98% 20%, 108% 20%, 118% 20%, 32% 20%, 42% 20%, 52% 20%, 62% 20%, 72% 20%, 82% 20%, 92% 20%, 102% 20%, 112% 20%, 22% 20%, 27% 20%, 37% 20%, 47% 20%, 57% 20%, 67% 20%, 77% 20%, 87% 20%, 97% 20%, 107% 20%, 117% 20%, 23% 20%, 33% 20%, 43% 20%, 53% 20%, 63% 20%, 73% 20%, 83% 20%, 93% 20%, 103% 20%, 113% 20%, 29% 20%, 39% 20%, 49% 20%; }
50% { background-position: 45% 50%, 55% 50%, 65% 50%, 75% 50%, 85% 50%, 95% 50%, 105% 50%, 115% 50%, 125% 50%, 135% 50%, 48% 50%, 58% 50%, 68% 50%, 78% 50%, 88% 50%, 98% 50%, 108% 50%, 118% 50%, 128% 50%, 138% 50%, 52% 50%, 62% 50%, 72% 50%, 82% 50%, 92% 50%, 102% 50%, 112% 50%, 122% 50%, 132% 50%, 42% 50%, 47% 50%, 57% 50%, 67% 50%, 77% 50%, 87% 50%, 97% 50%, 107% 50%, 117% 50%, 127% 50%, 137% 50%, 43% 50%, 53% 50%, 63% 50%, 73% 50%, 83% 50%, 93% 50%, 103% 50%, 113% 50%, 123% 50%, 133% 50%, 49% 50%, 59% 50%, 69% 50%; }
75% { background-position: 65% 80%, 75% 80%, 85% 80%, 95% 80%, 105% 80%, 115% 80%, 125% 80%, 135% 80%, 145% 80%, 155% 80%, 68% 80%, 78% 80%, 88% 80%, 98% 80%, 108% 80%, 118% 80%, 128% 80%, 138% 80%, 148% 80%, 158% 80%, 72% 80%, 82% 80%, 92% 80%, 102% 80%, 112% 80%, 122% 80%, 132% 80%, 142% 80%, 152% 80%, 62% 80%, 67% 80%, 77% 80%, 87% 80%, 97% 80%, 107% 80%, 117% 80%, 127% 80%, 137% 80%, 147% 80%, 157% 80%, 63% 80%, 73% 80%, 83% 80%, 93% 80%, 103% 80%, 113% 80%, 123% 80%, 133% 80%, 143% 80%, 153% 80%, 69% 80%, 79% 80%, 89% 80%; }
100% { background-position: 85% 50%, 95% 50%, 105% 50%, 115% 50%, 125% 50%, 135% 50%, 145% 50%, 155% 50%, 165% 50%, 175% 50%, 88% 50%, 98% 50%, 108% 50%, 118% 50%, 128% 50%, 138% 50%, 148% 50%, 158% 50%, 168% 50%, 178% 50%, 92% 50%, 102% 50%, 112% 50%, 122% 50%, 132% 50%, 142% 50%, 152% 50%, 162% 50%, 172% 50%, 82% 50%, 87% 50%, 97% 50%, 107% 50%, 117% 50%, 127% 50%, 137% 50%, 147% 50%, 157% 50%, 167% 50%, 177% 50%, 83% 50%, 93% 50%, 103% 50%, 113% 50%, 123% 50%, 133% 50%, 143% 50%, 153% 50%, 163% 50%, 173% 50%, 89% 50%, 99% 50%, 109% 50%; }
}

h6 {
font-weight: bold !important;
background: linear-gradient(90deg,
  #ff0000 0%,   /* 红 */
  #ff3300 8%,   /* 红橙 */
  #ff6600 16%,  /* 橙 */
  #ff9900 24%,  /* 深橙 */
  #ffcc00 32%,  /* 金橙 */
  #ffff00 40%,  /* 黄 */
  #ccff00 48%,  /* 黄绿 */
  #66ff00 56%,  /* 绿黄 */
  #00ff00 64%,  /* 绿 */
  #00ff66 72%,  /* 绿青 */
  #00ffcc 80%,  /* 青绿 */
  #00ccff 88%,  /* 青 */
  #0066ff 96%,  /* 蓝 */
  #ff0000 100%  /* 回到红色形成循环 */
) !important;
background-size: 400% auto !important;
-webkit-background-clip: text !important;
background-clip: text !important;
-webkit-text-fill-color: transparent !important;
animation: rainbow-flow 8s ease-in-out infinite !important;
position: relative !important;
z-index: 1 !important;
/* 恢复文字选择功能 */
user-select: text !important;
-webkit-user-select: text !important;
-moz-user-select: text !important;
}

/* 修复h6和其他文字选择问题 */
h6::selection {
background: rgba(0, 123, 255, 0.3) !important;
color: #ffffff !important;
}

h6::-moz-selection {
background: rgba(0, 123, 255, 0.3) !important;
color: #ffffff !important;
}

/* 修复 .css-1u0w142 文字选择问题 */
.css-1u0w142 {
user-select: text !important;
-webkit-user-select: text !important;
-moz-user-select: text !important;
}

.css-1u0w142::selection {
background: rgba(0, 123, 255, 0.3) !important;
color: #ffffff !important;
}

.css-1u0w142::-moz-selection {
background: rgba(0, 123, 255, 0.3) !important;
color: #ffffff !important;
}



@keyframes rainbow-flow {
0% {
  background-position: 0% center;
  filter: brightness(1) saturate(1);
}
25% {
  background-position: 100% center;
  filter: brightness(1.1) saturate(1.2);
}
50% {
  background-position: 200% center;
  filter: brightness(0.9) saturate(0.8);
}
75% {
  background-position: 300% center;
  filter: brightness(1.2) saturate(1.3);
}
100% {
  background-position: 400% center;
  filter: brightness(1) saturate(1);
}
}